# LLaOA: Large Language and Omics Assistant

## Overview

LLaOA (Large Language and Omics Assistant) is a specialized framework designed to bridge the gap between large language models (LLMs) and omics data analysis. It adapts the architecture of LLaVA (Large Language and Vision Assistant) by replacing the vision encoder with an omics encoder, specifically COMPASS, to enable natural language interactions with genomic data.

The framework allows researchers and clinicians to query omics data (such as RNA sequencing) using natural language questions and receive comprehensive, contextually relevant responses. This creates a powerful interface for interpreting complex biological data without requiring specialized programming knowledge.

## Key Features

- **Natural Language Interface**: Query complex omics data using simple natural language questions
- **Modular Architecture**: Easily extensible with different language models and omics encoders
- **Pre-trained Components**: Leverages pre-trained COMPASS models for omics understanding
- **Multi-level Feature Extraction**: Supports gene-level, geneset-level, and concept-level features
- **Comprehensive Evaluation**: Built-in metrics and visualization tools for model assessment
- **Customizable Training**: Flexible training options including LoRA fine-tuning

## Architecture

LLaOA follows a modular architecture with three main components that work together to process omics data and generate natural language responses:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  Omics Encoder  │───►│    Projector    │───►│ Language Model  │
│    (COMPASS)    │    │                 │    │                 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1. Omics Encoder (COMPASS)

The omics encoder processes raw omics data (e.g., RNA-seq) and extracts meaningful features. LLaOA uses COMPASS as the default encoder, which provides:

- **Gene-level Features**: 15,672-dimensional vectors representing individual gene expression
- **Geneset-level Features**: 133-dimensional vectors representing pathway activities
- **Concept-level Features**: 44-dimensional vectors representing higher-level biological concepts
- **Vector Embeddings**: 32-dimensional dense vector representations of omics profiles

The encoder transforms raw gene expression matrices into biologically meaningful representations that capture complex patterns and relationships in the data.

#### Technical Implementation

The COMPASS encoder is implemented as a PyTorch module (`COMPASSOmicsTower`) that wraps the COMPASS model. It handles:

- **Data Validation**: Ensures input data is in the correct format (pandas DataFrame)
- **Feature Selection**: Extracts different types of features based on configuration
- **Batch Processing**: Processes data in batches for memory efficiency
- **Device Management**: Handles computation on CPU or GPU
- **Dimensionality Handling**: Manages the output dimensions based on feature type

The encoder's forward pass takes a pandas DataFrame of RNA-seq data and returns a tensor of shape `[batch_size, 1, feature_dim]`, where `feature_dim` depends on the selected feature type.

### 2. Projector

The projector maps the features from the omics encoder to the language model's embedding space. This critical component enables the language model to understand and reason about omics data. LLaOA supports multiple projector architectures:

- **Linear**: Simple linear projection (fastest) - Direct mapping from omics features to language model dimensions
- **MLP2x_GELU**: Two-layer MLP with GELU activation (balanced) - Adds non-linearity and increased expressivity
- **MLP3x_GELU**: Three-layer MLP with GELU activation (most expressive) - Highest capacity for complex mappings

#### Technical Implementation

The projector is implemented as a PyTorch module that can be configured at initialization:

- **Input Dimension**: Automatically determined based on the omics feature type
- **Output Dimension**: Matches the hidden dimension of the language model
- **Intermediate Dimensions**: For MLP variants, typically 2x the input dimension
- **Activation Functions**: GELU (Gaussian Error Linear Unit) for better gradient flow
- **Layer Configuration**: Dynamically constructed based on the specified architecture

The projector is trained during the fine-tuning process to align omics representations with the language model's understanding. It learns to map biologically meaningful features to semantically meaningful embeddings in the language model's space.

### 3. Language Model

The language model generates responses based on the projected omics features and text prompts. LLaOA supports various language models:

- **LLaMA** (default): Meta's open-source LLM family (7B, 13B, 70B parameter variants)
- **Mistral**: High-performance open-source LLM with strong reasoning capabilities
- **MPT**: MosaicML's Pretrained Transformer models optimized for specific tasks

#### Technical Implementation

The language model integration is handled through a wrapper class (`LlaoaMetaForCausalLM`) that:

- **Prepares Inputs**: Combines omics features with text tokens
- **Manages Attention**: Extends attention masks to include omics tokens
- **Handles Position IDs**: Assigns appropriate position IDs to omics and text tokens
- **Loss Calculation**: Masks omics tokens during loss computation (using -100 labels)
- **Generation**: Provides specialized generation methods that incorporate omics data

The model processes inputs through these steps:
1. Encode omics data using the encoder and projector
2. Embed text tokens using the language model's token embedder
3. Concatenate omics features with text embeddings
4. Adjust attention masks and position IDs accordingly
5. Process the combined embeddings through the language model
6. Generate text output based on both omics and text inputs

This architecture allows the model to reason about omics data in the context of natural language questions, creating a seamless interface between biological data and human-readable responses.

## Data Requirements

### Omics Data

LLaOA expects RNA-seq data in a tab-separated values (TSV) format with the following structure:

- Rows represent samples
- Columns represent genes
- The first column can optionally contain sample IDs
- Gene expression values should be normalized (e.g., TPM, FPKM, or log-transformed counts)

Example:
```
sample_id   GENE1   GENE2   GENE3   ...
sample1     10.5    2.3     0.0     ...
sample2     5.2     1.7     3.1     ...
...
```

### Question-Answer Pairs

Question-answer pairs should be provided in a JSON file with the following format:

```json
[
  {
    "sample_id": "sample1",
    "question": "What is the predicted response to immunotherapy for this patient?",
    "answer": "Based on the gene expression profile, this patient is likely to respond to immunotherapy."
  },
  {
    "sample_id": "sample2",
    "question": "Analyze the immune profile of this patient based on gene expression.",
    "answer": "The patient shows elevated expression of immune-related genes, suggesting an inflamed tumor microenvironment."
  },
  ...
]
```

## Feature Generation Process

LLaOA processes omics data through several sophisticated stages to transform raw gene expression data into representations that language models can understand and reason about:

### 1. Data Preprocessing and Normalization

Before feeding data into the model, LLaOA performs several preprocessing steps:

- **Missing Value Handling**: Imputes missing values using techniques like mean imputation or KNN imputation
- **Outlier Detection**: Identifies and handles extreme values that could skew results
- **Log Transformation**: Often applies log2(x+1) transformation to compress the dynamic range of expression values
- **Scaling**: Normalizes data using methods such as:
  - **TPM (Transcripts Per Million)**: Adjusts for both gene length and sequencing depth
  - **FPKM/RPKM**: Fragments/Reads Per Kilobase of transcript per Million mapped reads
  - **Z-score Normalization**: Standardizes each gene to have mean 0 and standard deviation 1
  - **Quantile Normalization**: Makes the distribution of expression values identical across samples

The `normalize_expression` utility in the LLaOA data processing module handles these transformations automatically.

### 2. Feature Extraction via COMPASS

The COMPASS encoder extracts multiple levels of features from the normalized data:

#### Gene-Level Features (15,672 dimensions)
- **Direct Expression Values**: Captures the expression level of each individual gene
- **Technical Details**:
  - Implemented in `model.extract()` with `with_gene_level=True`
  - Returns a DataFrame with genes as columns and samples as rows
  - Preserves the full dimensionality of the original data
  - Useful for fine-grained analysis of specific gene expression patterns

#### Geneset-Level Features (133 dimensions)
- **Pathway Activity Scores**: Aggregates gene expression into functional pathway scores
- **Included Pathways**:
  - Hallmark gene sets (50 pathways representing well-defined biological states)
  - Immune signatures (e.g., cytokine signaling, T-cell activation)
  - Cancer-related pathways (e.g., proliferation, angiogenesis)
- **Technical Details**:
  - Computed using single-sample GSEA (ssGSEA) or similar methods
  - Reduces dimensionality while preserving biological meaning
  - More robust to technical noise than individual gene measurements

#### Concept-Level Features (44 dimensions)
- **High-Level Biological Concepts**: Represents abstract biological processes and states
- **Examples**:
  - Immune infiltration levels
  - Cell cycle activity
  - Stemness scores
  - Tumor microenvironment characteristics
- **Technical Details**:
  - Derived from geneset features using dimensionality reduction techniques
  - Provides the most compact and interpretable representation
  - Captures the most essential biological information

#### Vector Embeddings (32 dimensions)
- **Dense Representations**: Compact vectors that encode the essential information
- **Technical Details**:
  - Generated using the `model.project()` method
  - Created through a learned projection of concept-level features
  - Optimized to preserve similarity relationships between samples
  - Most efficient representation for integration with language models

### 3. Projection to Language Model Space

The extracted features must be mapped to the language model's embedding space:

- **Dimension Matching**: Transforms feature vectors to match the language model's hidden dimension (typically 768-4096)
- **Semantic Alignment**: Aligns biological meaning with the language model's semantic understanding
- **Implementation Details**:
  - Linear projector: `W * x + b` where W is a learned weight matrix
  - MLP projectors: Add non-linear transformations via GELU activations
  - Trained end-to-end during model fine-tuning
  - Learns to map biological patterns to concepts the language model can understand

### 4. Integration with Text Embeddings

The final stage combines omics features with text embeddings:

- **Token Concatenation**: Prepends projected omics features to the token embeddings
- **Attention Mask Adjustment**: Extends attention masks to include omics tokens
- **Position ID Assignment**: Assigns special position IDs to omics tokens
- **Label Handling**: Masks omics tokens during loss computation with -100 labels

This integration allows the language model to attend to both the omics data and the text query when generating responses, creating a multimodal reasoning capability.

The entire feature generation pipeline is optimized for both biological relevance and computational efficiency, allowing LLaOA to extract meaningful patterns from complex omics data and represent them in a way that language models can effectively utilize.

## Training Process

Training a LLaOA model involves a sophisticated process that combines omics data with natural language. The framework provides a comprehensive training pipeline with multiple configuration options to suit different computational resources and use cases.

### 1. Data Preparation

The first step involves preparing both omics data and question-answer pairs:

#### Omics Data Processing
- **Format Conversion**: Ensures data is in the required TSV format
- **Quality Control**: Filters low-quality or unreliable measurements
- **Normalization**: Applies appropriate normalization methods (see Feature Generation section)
- **Gene Selection**: Optionally filters to include only relevant genes
- **Train/Validation Split**: Divides data into training and validation sets (typically 90/10 split)

#### Question-Answer Pair Creation
- **Question Formulation**: Creates diverse, domain-specific questions about the omics data
- **Answer Generation**: Provides detailed, accurate answers based on domain knowledge
- **Validation**: Ensures questions and answers are scientifically accurate
- **Augmentation**: Optionally creates variations of questions to increase training diversity

The `OmicsQADataset` class handles the integration of omics data with QA pairs, creating a unified dataset for training.

### 2. Model Initialization

The model initialization process involves setting up all components of the architecture:

#### Language Model Loading
- **Base Model Selection**: Chooses an appropriate pre-trained LLM (LLaMA, Mistral, MPT)
- **Tokenizer Configuration**: Sets up the tokenizer with appropriate special tokens
- **Model Configuration**: Configures model parameters like context length and generation settings

#### COMPASS Encoder Integration
- **Encoder Loading**: Loads a pre-trained COMPASS model
- **Feature Type Selection**: Configures which feature level to use (gene, geneset, concept, or vector)
- **Batch Size Configuration**: Sets appropriate batch sizes for feature extraction

#### Projector Setup
- **Architecture Selection**: Chooses between linear, MLP2x_GELU, or MLP3x_GELU
- **Dimension Configuration**: Automatically configures input and output dimensions
- **Weight Initialization**: Initializes projector weights for optimal training

The `create_model_and_tokenizer` function handles this entire process, creating a fully initialized model ready for training.

### 3. Training Configuration

LLaOA offers flexible training configurations to accommodate different computational resources and use cases:

#### Training Modes

- **Full Fine-tuning**: Updates all parameters in the language model
  - Highest performance but requires significant GPU memory (24GB+ recommended)
  - Suitable when maximum performance is required
  - Example command: `--lora-rank 0`

- **LoRA Fine-tuning**: Uses Low-Rank Adaptation for parameter-efficient fine-tuning
  - Significantly reduces memory requirements (8GB+ GPU)
  - Nearly matches full fine-tuning performance with ~1% of the parameters
  - Configurable rank (typically 8-64) controls parameter count vs. performance
  - Example command: `--lora-rank 16 --lora-alpha 32 --lora-target-modules "q_proj,v_proj"`

- **Projector-only**: Freezes the encoder and language model, only training the projector
  - Most memory-efficient option (can run on 4GB+ GPU)
  - Suitable for initial experiments or resource-constrained environments
  - Example command: `--freeze-backbone`

#### Optimization Parameters

- **Learning Rate**: Typically 1e-5 to 5e-5 for full fine-tuning, 1e-4 for LoRA
- **Batch Size**: Adjusted based on available GPU memory (4-32 per device)
- **Gradient Accumulation**: Simulates larger batch sizes on limited hardware
- **Weight Decay**: Usually set to 0.01 for regularization
- **Learning Rate Schedule**: Cosine schedule with warmup (typically 10% of steps)
- **Training Duration**: Usually 3-10 epochs depending on dataset size

### 4. Training Loop

The training process follows these steps:

1. **Batch Processing**:
   - Load batch of omics data and QA pairs
   - Process omics data through the encoder
   - Tokenize questions and answers
   - Combine omics features with question tokens

2. **Forward Pass**:
   - Pass combined inputs through the model
   - Generate predictions for answer tokens
   - Calculate loss using cross-entropy (ignoring omics tokens)

3. **Backward Pass**:
   - Compute gradients with respect to loss
   - Apply gradient clipping (typically max norm of 1.0)
   - Update model parameters using AdamW optimizer

4. **Monitoring**:
   - Track training loss
   - Periodically evaluate on validation set
   - Save checkpoints at regular intervals

5. **Adaptation**:
   - Adjust learning rate according to schedule
   - Monitor for overfitting and apply early stopping if needed

The entire training process is managed by the `train.py` script, which handles data loading, model initialization, training loop, and checkpoint saving.

### 5. Checkpointing and Model Saving

LLaOA automatically saves model checkpoints during training:

- **Regular Checkpoints**: Saved every N steps (configurable)
- **Epoch Checkpoints**: Saved at the end of each epoch
- **Final Model**: Comprehensive save of the entire model at the end of training
- **Best Model**: Optionally saves the model with the best validation performance

These checkpoints include:
- Model weights
- Optimizer state
- Training configuration
- Tokenizer configuration

This allows for training resumption and ensures no work is lost in case of interruptions.

## Evaluation Metrics

LLaOA provides comprehensive evaluation tools to assess model performance from multiple perspectives. These metrics help users understand both the factual accuracy and linguistic quality of the model's responses to omics-related questions.

### Text Generation Metrics

When evaluating the model in generation mode (`--generation-mode`), LLaOA computes several text-based metrics:

#### BLEU Score
- **Description**: Measures n-gram overlap between generated and reference texts
- **Implementation**: Uses NLTK's `sentence_bleu` with smoothing
- **Variants**:
  - BLEU-1 (unigram overlap)
  - BLEU-2 (bigram overlap)
  - BLEU-3 (trigram overlap)
  - BLEU-4 (4-gram overlap)
- **Interpretation**: Higher is better, with 1.0 being perfect match
- **Strengths**: Good for assessing exact phrasing and terminology usage
- **Limitations**: Doesn't capture semantic similarity well

#### ROUGE Score
- **Description**: Recall-Oriented Understudy for Gisting Evaluation
- **Implementation**: Uses the `rouge_score` package
- **Variants**:
  - ROUGE-1: Unigram overlap
  - ROUGE-2: Bigram overlap
  - ROUGE-L: Longest Common Subsequence
- **Components**:
  - Precision: Proportion of generated n-grams that appear in reference
  - Recall: Proportion of reference n-grams that appear in generation
  - F1: Harmonic mean of precision and recall
- **Interpretation**: Higher is better, with 1.0 being perfect match
- **Use Case**: Good for assessing content coverage and summary quality

#### BERTScore
- **Description**: Uses contextual embeddings to measure semantic similarity
- **Implementation**: Computes cosine similarity between BERT embeddings
- **Components**:
  - Precision: Semantic precision of generated text
  - Recall: Semantic recall of reference content
  - F1: Harmonic mean of semantic precision and recall
- **Interpretation**: Higher is better, with 1.0 being perfect semantic match
- **Advantages**: Captures semantic similarity even with different wording
- **Use Case**: Best metric for assessing if the meaning is preserved

#### Exact Match Rate
- **Description**: Percentage of responses that exactly match the reference
- **Interpretation**: Higher is better, but typically very low for free-form text
- **Use Case**: Useful for factual questions with specific, concise answers

### Classification Metrics

When evaluating in classification mode (default), LLaOA computes token-level metrics:

#### Accuracy
- **Description**: Proportion of correctly predicted tokens
- **Implementation**: Uses scikit-learn's `accuracy_score`
- **Interpretation**: Higher is better, with 1.0 being perfect prediction
- **Use Case**: Good overall measure of model performance

#### F1 Score
- **Description**: Harmonic mean of precision and recall
- **Implementation**: Uses scikit-learn's `f1_score` with macro averaging
- **Interpretation**: Higher is better, with 1.0 being perfect
- **Use Case**: Balanced metric for uneven token distributions

#### Precision
- **Description**: Proportion of predicted positive tokens that are actually positive
- **Implementation**: Uses scikit-learn's `precision_score` with macro averaging
- **Interpretation**: Higher is better, with 1.0 being perfect precision
- **Use Case**: Important when false positives are costly

#### Recall
- **Description**: Proportion of actual positive tokens that are correctly predicted
- **Implementation**: Uses scikit-learn's `recall_score` with macro averaging
- **Interpretation**: Higher is better, with 1.0 being perfect recall
- **Use Case**: Important when false negatives are costly

### Visualization Tools

LLaOA generates various visualizations to help interpret model performance:

#### Text Length Distribution
- **Description**: Histogram comparing lengths of generated vs. reference texts
- **File**: `visualizations/text_length_distribution.png`
- **Use Case**: Identifies if model generates too verbose or too concise responses

#### Word Clouds
- **Description**: Visual representation of most frequent words in generated and reference texts
- **Files**:
  - `visualizations/generated_wordcloud.png`
  - `visualizations/reference_wordcloud.png`
- **Use Case**: Quickly identifies vocabulary differences and common terms

#### Metrics by Sample
- **Description**: Bar charts showing per-sample performance across metrics
- **File**: `visualizations/metrics_by_sample.png`
- **Use Case**: Identifies specific samples where model performs well or poorly

#### Confusion Matrix
- **Description**: Matrix showing predicted vs. actual token classes
- **File**: `visualizations/confusion_matrix.png`
- **Use Case**: Identifies specific token types the model struggles with

#### HTML Report
- **Description**: Interactive report with side-by-side comparisons
- **File**: `report.html`
- **Contents**:
  - Summary statistics
  - Sample-by-sample comparison
  - Highlighted differences
  - Interactive visualizations
- **Use Case**: Comprehensive analysis and sharing results with stakeholders

### Practical Usage for End Users

To evaluate a trained LLaOA model, use the `run_eval.py` script:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "path/to/rna_seq_data.tsv" \
    --qa-json-path "path/to/qa_pairs.json" \
    --output-dir "./eval_results" \
    --per-device-eval-batch-size 4 \
    --generation-mode
```

Key parameters for evaluation:

- **--generation-mode**: Enable to evaluate text generation (recommended for most use cases)
- **--max-new-tokens**: Maximum number of tokens to generate (default: 128)
- **--num-beams**: Number of beams for beam search (higher values = better quality but slower)
- **--do-sample**: Enable sampling-based generation (adds diversity)
- **--temperature**: Controls randomness in sampling (0.7-0.9 recommended)

After evaluation completes, review the following files:
1. `results.json`: Contains all computed metrics
2. `generated_texts.csv`: Side-by-side comparison of questions, generated answers, and references
3. `visualizations/`: Directory containing all visualization files
4. `report.html`: Interactive report for comprehensive analysis

For the best assessment of model quality, focus on:
1. BERTScore F1: Best indicator of semantic correctness
2. ROUGE-L F1: Good measure of content coverage
3. Sample-level analysis: Review individual examples to understand model strengths and weaknesses

## Getting Started

### Installation

1. Clone the repository:
```bash
git clone https://github.com/azu-oncology-rd/LLaOA.git
cd LLaOA
```

2. Install dependencies:
```bash
pip install -e .
# For training
pip install -e ".[train]"
```

### Training

To train a LLaOA model:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

### Evaluation

To evaluate a trained LLaOA model:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results" \
    --per-device-eval-batch-size 4 \
    --generation-mode
```

## Extending LLaOA

LLaOA is designed to be modular and extensible:

- **Custom Omics Encoders**: Integrate specialized encoders for different omics data types
- **Alternative Projectors**: Implement custom projection architectures
- **Different Language Models**: Support for various LLM architectures
- **Data Processing**: Customize preprocessing for specific omics data types
