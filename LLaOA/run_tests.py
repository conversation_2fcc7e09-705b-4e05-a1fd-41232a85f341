#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unified test script for LLaOA.

This script provides a single entry point for running all types of tests:
- Unit tests (TestConfig, TestOmicsEncoder, etc.)
- Model component tests (encoder, projector, model loading, etc.)

Usage:
    # Run all tests
    python run_tests.py --all

    # Run only unit tests
    python run_tests.py --unit-tests

    # Run only model tests
    python run_tests.py --model-tests

    # Run specific unit test module
    python run_tests.py --unit-tests --test-module TestConfig

    # Run specific unit test method
    python run_tests.py --unit-tests --test-module TestConfig --test-method test_omics_tower_config

    # Run model tests with specific model
    python run_tests.py --model-tests --compass-model-path /path/to/compass --model-path /path/to/model
"""

import os
import sys
import unittest
import argparse
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
from pathlib import Path

# Ensure the package is in the Python path
current_dir = Path(__file__).parent.absolute()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from llaoa.testing.model_tests import run_all_tests, test_model_components

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Run tests for LLaOA")

    # Test type options
    test_type = parser.add_argument_group("Test Types")
    test_type.add_argument("--all", action="store_true",
                        help="Run all tests (unit tests and model tests)")
    test_type.add_argument("--unit-tests", action="store_true",
                        help="Run unit tests")
    test_type.add_argument("--model-tests", action="store_true",
                        help="Run model component tests")

    # Unit test options
    unit_test = parser.add_argument_group("Unit Test Options")
    unit_test.add_argument("--test-module", type=str, default=None,
                        help="Specific test module to run (e.g., 'TestConfig')")
    unit_test.add_argument("--test-method", type=str, default=None,
                        help="Specific test method to run (e.g., 'test_omics_tower_config')")

    # Model test options
    model_test = parser.add_argument_group("Model Test Options")
    model_test.add_argument("--compass-model-path", type=str, default="./COMPASS/example/model/pretrainer.pt",
                        help="Path to the COMPASS model for model tests")
    model_test.add_argument("--model-path", type=str, default=None,
                        help="Path to the LLaOA model for model tests")
    model_test.add_argument("--model-base", type=str, default=None,
                        help="Base model for LoRA for model tests")
    model_test.add_argument("--sample-data-path", type=str, default=None,
                        help="Path to sample RNAseq data for model tests")
    model_test.add_argument("--feature-type", type=str,
                        choices=["gene_level", "geneset_level", "concept_level", "vector"],
                        help="Test specific feature type")
    model_test.add_argument("--projector-type", type=str,
                        choices=["linear", "mlp2x_gelu", "mlp3x_gelu"],
                        help="Test specific projector type")
    model_test.add_argument("--model-type", type=str,
                        choices=["llama", "mistral", "mpt"],
                        help="Test specific model type")

    # Output options
    output = parser.add_argument_group("Output Options")
    output.add_argument("--output-dir", type=str, default="./test_results",
                        help="Directory to save test results")
    output.add_argument("--verbose", action="store_true",
                        help="Verbose output")

    args = parser.parse_args()

    # If no test type is specified, default to running all tests
    if not (args.all or args.unit_tests or args.model_tests):
        args.all = True

    # If --all is specified, run both unit tests and model tests
    if args.all:
        args.unit_tests = True
        args.model_tests = True

    return args

def run_unit_tests(args):
    """Run unit tests."""
    logger.info("Running unit tests...")

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Set up file logging
    file_handler = logging.FileHandler(os.path.join(args.output_dir, 'unit_test_log.txt'))
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logging.getLogger().addHandler(file_handler)

    # Import test modules
    from llaoa.testing.unit_tests import (
        TestConfig,
        TestOmicsEncoder,
        TestOmicsProjector,
        TestDataProcessing,
        TestOmicsQADataset
    )

    # Create test suite
    if args.test_module is not None:
        # Run specific test module
        test_class = globals()[args.test_module]
        if args.test_method is not None:
            # Run specific test method
            suite = unittest.TestSuite()
            suite.addTest(test_class(args.test_method))
        else:
            # Run all methods in the module
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
    else:
        # Run all tests
        suite = unittest.TestSuite()
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestConfig))
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestOmicsEncoder))
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestOmicsProjector))
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataProcessing))
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestOmicsQADataset))

    # Create test runner
    from unittest import TextTestRunner
    runner = TextTestRunner(verbosity=2 if args.verbose else 1)

    # Run tests and capture results
    result = runner.run(suite)

    # Save results
    with open(os.path.join(args.output_dir, 'unit_test_results.txt'), 'w') as f:
        f.write(f"Tests run: {result.testsRun}\n")
        f.write(f"Errors: {len(result.errors)}\n")
        f.write(f"Failures: {len(result.failures)}\n")

        if result.errors:
            f.write("\nErrors:\n")
            for test, error in result.errors:
                f.write(f"{test}: {error}\n")

        if result.failures:
            f.write("\nFailures:\n")
            for test, failure in result.failures:
                f.write(f"{test}: {failure}\n")

    # Print summary
    logger.info(f"Unit tests run: {result.testsRun}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Failures: {len(result.failures)}")

    return result.wasSuccessful()

def run_model_tests(args):
    """Run model component tests."""
    logger.info("Running model component tests...")

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Set up file logging
    file_handler = logging.FileHandler(os.path.join(args.output_dir, 'model_test_log.txt'))
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logging.getLogger().addHandler(file_handler)

    # Run model tests
    if args.feature_type or args.projector_type or args.model_type:
        # Run specific model component tests
        test_model_components(args)
        model_tests_success = True  # Assume success since test_model_components doesn't return a result
    else:
        # Run all model tests
        model_test_results = run_all_tests(
            compass_model_path=args.compass_model_path,
            model_path=args.model_path,
            model_base=args.model_base,
            sample_data_path=args.sample_data_path
        )

        # Save model test results
        import json
        with open(os.path.join(args.output_dir, 'model_test_results.json'), 'w') as f:
            json.dump(model_test_results, f, indent=2)

        # Print summary
        passed = sum(1 for result in model_test_results.values() if result)
        total = len(model_test_results)
        logger.info(f"Model tests passed: {passed}/{total} ({passed/total*100:.1f}%)")

        # Check if all tests passed
        model_tests_success = (passed == total)

    return model_tests_success

def main():
    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Track test results
    unit_tests_success = True
    model_tests_success = True

    # Run unit tests if requested
    if args.unit_tests:
        unit_tests_success = run_unit_tests(args)

    # Run model tests if requested
    if args.model_tests:
        model_tests_success = run_model_tests(args)

    # Exit with appropriate code
    if unit_tests_success and model_tests_success:
        logger.info("All tests passed!")
        sys.exit(0)
    else:
        logger.error("Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
