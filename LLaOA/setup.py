import os
import pkg_resources
from setuptools import setup, find_packages

def get_version():
    return "0.1.0"

def get_requirements():
    requirements = [
        "torch>=2.0.0",
        "transformers>=4.30.0",
        "pandas>=1.3.0",
        "numpy>=1.20.0",
        "scikit-learn>=1.0.0",
    ]
    return requirements

def get_train_requirements():
    requirements = [
        "accelerate>=0.20.0",
        "datasets>=2.10.0",
        "sentencepiece>=0.1.97",
        "protobuf>=3.20.0",
    ]
    return requirements

setup(
    name="llaoa",
    version=get_version(),
    author="Bala Suraj Pedasingu",
    author_email="<EMAIL>",
    description="Large Language and Omics Assistant",
    long_description=open("README.md", encoding="utf-8").read(),
    long_description_content_type="text/markdown",
    keywords="llm, omics, rnaseq, compass, llava",
    license="Apache 2.0",
    url="https://github.com/azu-oncology-rd/LLaOA",
    packages=find_packages(),
    python_requires=">=3.8.0",
    install_requires=get_requirements(),
    extras_require={
        "train": get_train_requirements(),
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
)
