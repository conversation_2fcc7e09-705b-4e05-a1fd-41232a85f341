#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run evaluation script for LLaOA with enhanced features.
"""

import os
import sys
import argparse
from typing import Optional
from pathlib import Path

# Ensure the package is in the Python path
current_dir = Path(__file__).parent.absolute()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

def parse_args():
    parser = argparse.ArgumentParser(description="Run LLaOA evaluation")

    # Model arguments
    parser.add_argument("--model-path", type=str, required=True,
                        help="Path to pretrained model")
    parser.add_argument("--model-base", type=str, default=None,
                        help="Base model for LoRA")

    # Data arguments
    parser.add_argument("--rna-seq-path", type=str,
                        default="COMPASS/example/data/compass_gide_tpm.tsv",
                        help="Path to RNAseq data")
    parser.add_argument("--qa-json-path", type=str, default="data/omics_qa.json",
                        help="Path to QA pairs JSON")
    parser.add_argument("--sample-id-col", type=str, default=None,
                        help="Column name for sample ID in RNAseq data")
    parser.add_argument("--max-length", type=int, default=512,
                        help="Maximum sequence length")

    # Evaluation arguments
    parser.add_argument("--output-dir", type=str, default="./eval_results",
                        help="Directory to save evaluation results")
    parser.add_argument("--per-device-eval-batch-size", type=int, default=4,
                        help="Batch size per device for evaluation")
    parser.add_argument("--generation-mode", action="store_true",
                        help="Use generation mode instead of classification")
    parser.add_argument("--max-new-tokens", type=int, default=128,
                        help="Maximum number of new tokens to generate")
    parser.add_argument("--num-beams", type=int, default=1,
                        help="Number of beams for beam search")
    parser.add_argument("--do-sample", action="store_true",
                        help="Whether to use sampling for generation")
    parser.add_argument("--temperature", type=float, default=1.0,
                        help="Temperature for sampling")
    parser.add_argument("--top-p", type=float, default=1.0,
                        help="Top-p sampling parameter")
    parser.add_argument("--top-k", type=int, default=50,
                        help="Top-k sampling parameter")
    parser.add_argument("--repetition-penalty", type=float, default=1.0,
                        help="Repetition penalty for generation")
    parser.add_argument("--length-penalty", type=float, default=1.0,
                        help="Length penalty for generation")
    parser.add_argument("--no-repeat-ngram-size", type=int, default=0,
                        help="Size of n-grams to prevent repetition")
    parser.add_argument("--fp16", action="store_true",
                        help="Use mixed precision evaluation (fp16)")
    parser.add_argument("--bf16", action="store_true",
                        help="Use mixed precision evaluation (bf16)")
    parser.add_argument("--dataloader-num-workers", type=int, default=4,
                        help="Number of dataloader workers")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")
    parser.add_argument("--local_rank", type=int, default=-1,
                        help="Local rank for distributed evaluation")

    return parser.parse_args()

def main():
    args = parse_args()

    # Convert arguments to format expected by HfArgumentParser
    model_args = [
        f"--model_path={args.model_path}",
        f"--model_base={args.model_base}" if args.model_base else "",
    ]

    data_args = [
        f"--rna_seq_path={args.rna_seq_path}",
        f"--qa_json_path={args.qa_json_path}",
        f"--sample_id_col={args.sample_id_col}" if args.sample_id_col else "",
        f"--max_length={args.max_length}",
    ]

    eval_args = [
        f"--output_dir={args.output_dir}",
        f"--per_device_eval_batch_size={args.per_device_eval_batch_size}",
        "--generation_mode" if args.generation_mode else "",
        f"--max_new_tokens={args.max_new_tokens}",
        f"--num_beams={args.num_beams}",
        "--do_sample" if args.do_sample else "",
        f"--temperature={args.temperature}",
        f"--top_p={args.top_p}",
        f"--top_k={args.top_k}",
        f"--repetition_penalty={args.repetition_penalty}",
        f"--length_penalty={args.length_penalty}",
        f"--no_repeat_ngram_size={args.no_repeat_ngram_size}",
        "--fp16" if args.fp16 else "",
        "--bf16" if args.bf16 else "",
        f"--dataloader_num_workers={args.dataloader_num_workers}",
        f"--seed={args.seed}",
        f"--local_rank={args.local_rank}",
    ]

    # Filter out empty arguments
    model_args = [arg for arg in model_args if arg]
    data_args = [arg for arg in data_args if arg]
    eval_args = [arg for arg in eval_args if arg]

    # Combine all arguments
    all_args = model_args + data_args + eval_args

    # Update sys.argv
    sys.argv = [sys.argv[0]] + all_args

    # Import and run evaluation
    from llaoa.eval.eval_qa import evaluate
    evaluate()

if __name__ == "__main__":
    main()
