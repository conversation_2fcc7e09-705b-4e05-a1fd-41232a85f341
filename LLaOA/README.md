# LLaOA: Large Language and Omics Assistant

LLaOA is a variant of the LLaVA framework designed to support question answering on omics data (e.g., RNAseq) by integrating the COMPASS encoder in place of the vision encoder. It enables training and evaluation of large language models on omics-based question answering tasks, following the modular and extensible design of LLaVA.

## Key Features
- Modular omics encoder integration (COMPASS)
- Omics-to-language model projector
- Data pipeline for RNAseq QA
- Training and evaluation scripts for omics QA
- Extensible for other omics data types

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/LLaOA.git
cd LLaOA
```

2. Install dependencies:
```bash
pip install -e .
# For training
pip install -e ".[train]"
```

3. Install COMPASS:
```bash
git clone https://github.com/mims-harvard/COMPASS.git
cd COMPASS
pip install -r requirements.txt
```

### Training

To train a LLaOA model:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

### Evaluation

To evaluate a trained LLaOA model:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results" \
    --per-device-eval-batch-size 4 \
    --generation-mode
```

## Documentation

Comprehensive documentation is available in the [docs](docs/) directory:

- [Architecture Overview](docs/architecture.md): Detailed explanation of the LLaOA architecture
- [Installation Guide](docs/installation.md): Step-by-step installation instructions
- [Data Preparation](docs/data_preparation.md): Guide for preparing omics data and QA pairs
- [Training Guide](docs/training.md): Instructions for training LLaOA models
- [Evaluation Guide](docs/evaluation.md): Methods for evaluating LLaOA models
- [Customization Guide](docs/customization.md): How to extend LLaOA with custom components
- [API Reference](docs/api_reference.md): Detailed API documentation

## Example Notebooks

Interactive examples are available in the [examples](examples/) directory:

1. [Data Preparation](examples/01_data_preparation.ipynb): Preparing omics data and QA pairs
2. [Model Training](examples/02_model_training.ipynb): Training a LLaOA model
3. [Model Evaluation](examples/03_model_evaluation.ipynb): Evaluating a trained model
4. [Inference and Visualization](examples/04_inference_visualization.ipynb): Using a model for inference
5. [Custom Encoder Integration](examples/05_custom_encoder.ipynb): Integrating a custom omics encoder

## Directory Structure
```
LLaOA/
  ├── data/                      # Sample data files
  │   └── omics_qa.json          # Sample QA pairs
  ├── docs/                      # Documentation
  ├── examples/                  # Example notebooks
  ├── llaoa/                    # Main package
  │   ├── model/                 # Model components
  │   ├── data/                  # Data handling
  │   ├── train/                 # Training utilities
  │   ├── eval/                  # Evaluation utilities
  │   └── testing/               # Testing utilities
  ├── run_train.py               # Training script
  ├── run_eval.py                # Evaluation script
  ├── run_tests.py               # Test runner
  └── README.md                  # This file
```

## Data Format

### RNAseq Data

The RNAseq data should be in a TSV file with genes as columns and samples as rows. The first column can optionally be a sample ID.

### QA Pairs

The QA pairs should be in a JSON file with the following format:

```json
[
  {
    "sample_id": "sample1",
    "question": "What is the predicted response to immunotherapy for this patient?",
    "answer": "Based on the gene expression profile, this patient is likely to respond to immunotherapy."
  },
  ...
]
```

## Extending LLaOA

LLaOA is designed to be modular and extensible. See the [Customization Guide](docs/customization.md) for details on:

- Adding support for new language models
- Integrating custom omics encoders
- Creating custom projectors
- Customizing data processing

## License

This project is licensed under the same terms as LLaVA and COMPASS.