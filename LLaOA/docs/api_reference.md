# LLaOA API Reference

This document provides a comprehensive reference for the LLaOA API.

## Model Components

### LlaoaConfig

```python
class LlaoaConfig:
    """Configuration class for LLaOA models."""

    def __init__(
        model_type: str = "llama",  # Type of language model
        model_path: Optional[str] = None,  # Path to pretrained model
        model_base: Optional[str] = None,  # Base model for LoRA
        omics_encoder_config: OmicsEncoderConfig = None,  # Omics encoder config
        projector_type: str = "mlp2x_gelu",  # Type of projector
        lora_config: Optional[LoraConfig] = None,  # LoRA configuration
    ):
        ...
```

### OmicsEncoderConfig

```python
class OmicsEncoderConfig:
    """Configuration class for omics encoders."""

    def __init__(
        encoder_type: str = "compass",  # Type of encoder
        model_path: str = None,  # Path to pretrained encoder
        feature_type: str = "gene_level",  # Type of features to extract
    ):
        ...
```

### LoraConfig

```python
class LoraConfig:
    """Configuration class for LoRA."""

    def __init__(
        rank: int = 8,  # Rank of LoRA adaptation
        alpha: float = 16,  # Alpha parameter
        dropout: float = 0.05,  # Dropout probability
        target_modules: List[str] = None,  # Target modules
    ):
        ...
```

## Model Building

### build_llaoa_model

```python
def build_llaoa_model(config: LlaoaConfig) -> nn.Module:
    """
    Build a LLaOA model based on the configuration.

    Args:
        config: LLaOA configuration

    Returns:
        LLaOA model
    """
    ...
```

### load_llaoa_model

```python
def load_llaoa_model(
    model_path: str,
    model_base: Optional[str] = None,
    device: Optional[str] = None
) -> nn.Module:
    """
    Load a pretrained LLaOA model.

    Args:
        model_path: Path to the model
        model_base: Base model for LoRA (if applicable)
        device: Device to load the model on

    Returns:
        Loaded model
    """
    ...
```

### build_omics_encoder

```python
def build_omics_encoder(
    encoder_type: str,
    model_path: str,
    feature_type: str = "gene_level",
    **kwargs
) -> nn.Module:
    """
    Build an omics encoder.

    Args:
        encoder_type: Type of encoder
        model_path: Path to pretrained encoder
        feature_type: Type of features to extract
        **kwargs: Additional arguments

    Returns:
        Omics encoder
    """
    ...
```

### build_projector

```python
def build_projector(
    projector_type: str,
    input_dim: int,
    output_dim: int,
    **kwargs
) -> nn.Module:
    """
    Build a projector.

    Args:
        projector_type: Type of projector
        input_dim: Input dimension
        output_dim: Output dimension
        **kwargs: Additional arguments

    Returns:
        Projector module
    """
    ...
```

## Data Handling

### OmicsQADataset

```python
class OmicsQADataset(Dataset):
    """Dataset for omics QA tasks."""

    def __init__(
        self,
        rna_seq_path: str,
        qa_json_path: str,
        tokenizer,
        sample_id_col: Optional[str] = None,
        max_length: int = 512
    ):
        """
        Initialize the dataset.

        Args:
            rna_seq_path: Path to RNAseq data
            qa_json_path: Path to QA pairs
            tokenizer: Tokenizer for text processing
            sample_id_col: Column name for sample ID
            max_length: Maximum sequence length
        """
        ...

    def __len__(self) -> int:
        """Return the number of samples."""
        ...

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a sample by index."""
        ...
```

### load_rna_seq_data

```python
def load_rna_seq_data(
    file_path: str,
    sample_id_col: Optional[str] = None
) -> Tuple[pd.DataFrame, List[str]]:
    """
    Load RNAseq data from a TSV file.

    Args:
        file_path: Path to the TSV file
        sample_id_col: Column name for sample ID

    Returns:
        DataFrame with RNAseq data and list of sample IDs
    """
    ...
```

### load_qa_pairs

```python
def load_qa_pairs(file_path: str) -> List[Dict[str, str]]:
    """
    Load QA pairs from a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        List of QA pairs
    """
    ...
```

## Training

### train

```python
def train(
    model_args: ModelArguments,
    data_args: DataArguments,
    training_args: TrainingArguments
):
    """
    Train a LLaOA model.

    Args:
        model_args: Model arguments
        data_args: Data arguments
        training_args: Training arguments
    """
    ...
```

## Evaluation

### evaluate

```python
def evaluate(
    model_args: ModelArguments,
    data_args: DataArguments,
    eval_args: EvalArguments
):
    """
    Evaluate a LLaOA model.

    Args:
        model_args: Model arguments
        data_args: Data arguments
        eval_args: Evaluation arguments
    """
    ...
```

### evaluate_model

```python
def evaluate_model(
    model: nn.Module,
    dataset: Dataset,
    generation_mode: bool = False,
    max_new_tokens: int = 128,
    num_beams: int = 1,
    **kwargs
) -> Dict[str, Any]:
    """
    Evaluate a model on a dataset.

    Args:
        model: LLaOA model
        dataset: Evaluation dataset
        generation_mode: Whether to use generation mode
        max_new_tokens: Maximum number of tokens to generate
        num_beams: Number of beams for beam search
        **kwargs: Additional arguments

    Returns:
        Evaluation results
    """
    ...
```

## Metrics

### calculate_metrics

```python
def calculate_metrics(
    predictions: List[str],
    references: List[str]
) -> Dict[str, float]:
    """
    Calculate evaluation metrics.

    Args:
        predictions: Predicted answers
        references: Reference answers

    Returns:
        Dictionary of metrics
    """
    ...
```

## Visualization

### plot_metrics

```python
def plot_metrics(
    metrics_path: str,
    output_path: Optional[str] = None
):
    """
    Plot evaluation metrics.

    Args:
        metrics_path: Path to metrics JSON
        output_path: Path to save the plot
    """
    ...
```

### plot_confusion_matrix

```python
def plot_confusion_matrix(
    confusion_matrix_path: str,
    output_path: Optional[str] = None
):
    """
    Plot confusion matrix.

    Args:
        confusion_matrix_path: Path to confusion matrix JSON
        output_path: Path to save the plot
    """
    ...
```

## Utility Functions

### set_seed

```python
def set_seed(seed: int):
    """
    Set random seed for reproducibility.

    Args:
        seed: Random seed
    """
    ...
```

### get_device

```python
def get_device() -> torch.device:
    """
    Get the available device.

    Returns:
        torch.device: CUDA if available, else CPU
    """
    ...
```
