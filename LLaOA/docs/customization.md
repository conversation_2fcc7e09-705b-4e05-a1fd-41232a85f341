# Customization Guide for LLaOA

This guide explains how to extend and customize LLaOA for your specific needs.

## Overview

LLaOA is designed to be modular and extensible. You can customize:

1. **Omics Encoders**: Add support for different omics data types
2. **Language Models**: Integrate new language models
3. **Projectors**: Create custom projectors for mapping omics features to language model space
4. **Data Processing**: Customize data loading and preprocessing
5. **Training and Evaluation**: Modify training and evaluation procedures

## Adding a New Omics Encoder

### Step 1: Create a new encoder class

Create a new file in `llaoa/model/omics_encoder/` (e.g., `custom_encoder.py`):

```python
import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Tuple

class CustomOmicsEncoder(nn.Module):
    """Custom encoder for omics data."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        # Initialize your encoder components
        self.encoder = nn.Sequential(
            nn.Linear(config["input_dim"], config["hidden_dim"]),
            nn.ReLU(),
            nn.Linear(config["hidden_dim"], config["output_dim"])
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the encoder.

        Args:
            x: Input omics data tensor of shape (batch_size, input_dim)

        Returns:
            Encoded features of shape (batch_size, output_dim)
        """
        return self.encoder(x)

    @classmethod
    def from_pretrained(cls, model_path: str, config: Optional[Dict[str, Any]] = None) -> "CustomOmicsEncoder":
        """
        Load a pretrained encoder.

        Args:
            model_path: Path to the pretrained model
            config: Optional configuration dictionary

        Returns:
            Pretrained encoder instance
        """
        if config is None:
            config = torch.load(model_path)["config"]

        model = cls(config)
        model.load_state_dict(torch.load(model_path)["model"])
        return model
```

### Step 2: Update the encoder builder

Modify `llaoa/model/omics_encoder/builder.py` to include your new encoder:

```python
from .custom_encoder import CustomOmicsEncoder

def build_omics_encoder(encoder_type: str, **kwargs) -> nn.Module:
    """Build an omics encoder based on the specified type."""
    if encoder_type == "compass":
        return CompassEncoder(**kwargs)
    elif encoder_type == "custom":  # Add your encoder type
        return CustomOmicsEncoder(**kwargs)
    else:
        raise ValueError(f"Unsupported encoder type: {encoder_type}")
```

### Step 3: Update the configuration

Modify `llaoa/model/config.py` to include configuration for your encoder:

```python
@dataclass
class OmicsEncoderConfig:
    encoder_type: str = "compass"  # Options: "compass", "custom"
    model_path: str = None
    input_dim: int = 5000  # For custom encoder
    hidden_dim: int = 512  # For custom encoder
    output_dim: int = 256  # For custom encoder
```

## Adding a New Language Model

### Step 1: Create a new language model integration

Create a new file in `llaoa/model/language_model/` (e.g., `llaoa_gpt.py`):

```python
import torch
import torch.nn as nn
from transformers import GPTNeoForCausalLM, GPTNeoConfig
from typing import Dict, Any, Optional, Tuple

class LlaoaGPTModel(nn.Module):
    """LLaOA model with GPT-Neo as the language model."""

    def __init__(
        self,
        config: GPTNeoConfig,
        omics_embed_dim: int,
        **kwargs
    ):
        super().__init__()
        self.config = config
        self.language_model = GPTNeoForCausalLM(config)

        # Add omics projection to language model
        self.omics_proj = nn.Linear(omics_embed_dim, config.hidden_size)

    def forward(
        self,
        input_ids: torch.LongTensor,
        attention_mask: torch.Tensor,
        omics_features: torch.Tensor,
        labels: Optional[torch.LongTensor] = None,
    ) -> Tuple[torch.Tensor, ...]:
        """
        Forward pass through the model.

        Args:
            input_ids: Token IDs
            attention_mask: Attention mask
            omics_features: Encoded omics features
            labels: Optional labels for training

        Returns:
            Model outputs
        """
        # Project omics features to language model space
        projected_omics = self.omics_proj(omics_features)

        # Add omics features to the first token embeddings
        inputs_embeds = self.language_model.transformer.wte(input_ids)
        inputs_embeds[:, 0, :] += projected_omics

        # Forward pass through language model
        outputs = self.language_model(
            inputs_embeds=inputs_embeds,
            attention_mask=attention_mask,
            labels=labels,
            return_dict=True
        )

        return outputs
```

### Step 2: Update the model builder

Modify `llaoa/model/builder.py` to include your new language model:

```python
from .language_model.llaoa_gpt import LlaoaGPTModel

def build_llaoa_model(config: LlaoaConfig) -> nn.Module:
    """Build a LLaOA model based on the configuration."""
    # ... existing code ...

    if config.model_type == "llama":
        return LlaoaLlamaModel(...)
    elif config.model_type == "gpt":  # Add your model type
        return LlaoaGPTModel(...)
    # ... existing code ...
```

## Creating a Custom Projector

### Step 1: Create a new projector class

Modify `llaoa/model/omics_projector/builder.py` to include your custom projector:

```python
def build_projector(
    projector_type: str,
    input_dim: int,
    output_dim: int,
    **kwargs
) -> nn.Module:
    """Build a projector based on the specified type."""
    if projector_type == "linear":
        return nn.Linear(input_dim, output_dim)
    elif projector_type == "mlp2x_gelu":
        return nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.GELU(),
            nn.Linear(input_dim * 2, output_dim)
        )
    elif projector_type == "custom_residual":  # Add your projector type
        return nn.Sequential(
            nn.Linear(input_dim, input_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim, output_dim),
            nn.LayerNorm(output_dim)
        )
    # ... existing code ...
```

## Customizing Data Processing

### Creating a Custom Dataset

Create a new file in `llaoa/data/` (e.g., `custom_dataset.py`):

```python
import torch
from torch.utils.data import Dataset
from typing import Dict, List, Any

class CustomOmicsDataset(Dataset):
    """Custom dataset for omics data."""

    def __init__(self, data_path: str, tokenizer, max_length: int = 512):
        self.data = self.load_data(data_path)
        self.tokenizer = tokenizer
        self.max_length = max_length

    def load_data(self, data_path: str) -> List[Dict[str, Any]]:
        """Load and preprocess data."""
        # Implement your data loading logic
        return data

    def __len__(self) -> int:
        return len(self.data)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        item = self.data[idx]

        # Process text
        text_encoding = self.tokenizer(
            item["text"],
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )

        # Process omics data
        omics_data = torch.tensor(item["omics_data"], dtype=torch.float)

        return {
            "input_ids": text_encoding.input_ids.squeeze(),
            "attention_mask": text_encoding.attention_mask.squeeze(),
            "omics_data": omics_data,
            "labels": text_encoding.input_ids.squeeze() if "labels" in item else None
        }
```

## Next Steps

After customizing LLaOA:
- Test your changes thoroughly
- Document your extensions
- Consider contributing back to the main repository

For more examples, see the [Example Notebooks](../examples/) directory.
