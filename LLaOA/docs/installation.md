# LLaOA Installation Guide

This guide provides step-by-step instructions for installing LLaOA and its dependencies.

## Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (recommended for training)
- Git

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/LLaOA.git
cd LLaOA
```

### 2. Install LLaOA

Install the package in development mode:

```bash
pip install -e .
```

For training capabilities, install with the training extras:

```bash
pip install -e ".[train]"
```

### 3. Install COMPASS

LLaOA requires COMPASS for omics data encoding. Install it as follows:

```bash
git clone https://github.com/mims-harvard/COMPASS.git
cd COMPASS
pip install -r requirements.txt
```

### 4. Install Additional Dependencies (Optional)

For visualization and notebook support:

```bash
pip install matplotlib seaborn jupyter
```

## Verifying Installation

To verify that LLaOA is installed correctly, run the test script:

```bash
python run_unit_tests.py
```

## Common Issues and Solutions

### CUDA Compatibility

If you encounter CUDA-related errors, ensure that your PyTorch installation is compatible with your CUDA version:

```bash
# For CUDA 11.8
pip install torch==2.0.1+cu118 torchvision==0.15.2+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
```

### Memory Issues

If you encounter out-of-memory errors during training:
- Reduce batch size
- Use gradient accumulation
- Enable mixed precision training (fp16 or bf16)

### COMPASS Installation Issues

If you encounter issues installing COMPASS:
- Ensure you have the required system libraries (check COMPASS documentation)
- Try installing dependencies one by one to identify problematic packages

## Using a Virtual Environment (Recommended)

It's recommended to use a virtual environment for LLaOA:

```bash
# Using venv
python -m venv llaoa-env
source llaoa-env/bin/activate  # On Windows: llaoa-env\Scripts\activate

# Using conda
conda create -n llaoa python=3.8
conda activate llaoa
```

## Docker Installation (Alternative)

For a containerized setup, you can use Docker:

```bash
# Build the Docker image
docker build -t llaoa .

# Run the container
docker run --gpus all -it llaoa
```

## Next Steps

After installation, you can:
- Prepare your data following the [Data Preparation Guide](data_preparation.md)
- Train a model using the [Training Guide](training.md)
- Evaluate your model with the [Evaluation Guide](evaluation.md)
