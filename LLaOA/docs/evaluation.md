# Evaluation Guide for LLaOA

This guide explains how to evaluate LLaOA models on omics question-answering tasks.

## Prerequisites

Before evaluation, ensure you have:
- A trained LLaOA model
- Test data (omics data and QA pairs)
- Installed LLaOA and its dependencies (see [Installation Guide](installation.md))

## Basic Evaluation

### Command Line Interface

The simplest way to evaluate a LLaOA model is using the `run_eval.py` script:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results" \
    --per-device-eval-batch-size 4 \
    --generation-mode
```

### Key Parameters

- `--model-path`: Path to the trained model
- `--model-base`: Base model for LoRA (if applicable)
- `--rna-seq-path`: Path to the RNAseq data file
- `--qa-json-path`: Path to the QA pairs JSON file
- `--output-dir`: Directory to save evaluation results
- `--per-device-eval-batch-size`: Batch size per GPU
- `--generation-mode`: Use generation mode instead of classification

## Evaluation Modes

### Generation Mode

In generation mode, the model generates free-form answers to questions:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results_gen" \
    --generation-mode \
    --max-new-tokens 128 \
    --num-beams 4
```

Generation parameters:
- `--max-new-tokens`: Maximum number of tokens to generate
- `--num-beams`: Number of beams for beam search
- `--do-sample`: Use sampling instead of greedy decoding
- `--temperature`: Temperature for sampling
- `--top-p`: Top-p sampling parameter
- `--top-k`: Top-k sampling parameter

### Classification Mode

Without the `--generation-mode` flag, the model performs classification:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results_cls"
```

## Evaluation Metrics

LLaOA calculates various metrics depending on the evaluation mode:

### Generation Mode Metrics

- **BLEU**: Measures n-gram overlap between generated and reference answers
- **ROUGE**: Measures recall-oriented overlap
- **BERTScore**: Measures semantic similarity using BERT embeddings
- **Exact Match**: Percentage of exact matches between generated and reference answers

### Classification Mode Metrics

- **Accuracy**: Percentage of correct classifications
- **Precision**: Precision for each class
- **Recall**: Recall for each class
- **F1 Score**: Harmonic mean of precision and recall

## Visualizing Results

LLaOA includes visualization tools for evaluation results:

```python
from llaoa.eval.visualization import plot_metrics, plot_confusion_matrix

# Plot metrics
plot_metrics("./eval_results/metrics.json", output_path="./eval_results/metrics_plot.png")

# Plot confusion matrix (for classification)
plot_confusion_matrix("./eval_results/confusion_matrix.json", output_path="./eval_results/confusion_matrix.png")
```

## Advanced Evaluation Options

### Evaluating LoRA Models

For models trained with LoRA:

```bash
python run_eval.py \
    --model-path "./checkpoints_lora/final" \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results_lora" \
    --generation-mode
```

### Mixed Precision Evaluation

To speed up evaluation:

```bash
python run_eval.py \
    --model-path "./checkpoints/final" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./eval_results_fp16" \
    --fp16
```

### Custom Evaluation

For more control over the evaluation process, you can use the Python API:

```python
from llaoa.eval.eval_qa import evaluate_model
from llaoa.model.builder import load_llaoa_model
from llaoa.data.omics_qa_dataset import OmicsQADataset

# Load model
model = load_llaoa_model("./checkpoints/final")

# Load dataset
dataset = OmicsQADataset(
    rna_seq_path="COMPASS/example/data/compass_gide_tpm.tsv",
    qa_json_path="data/omics_qa.json"
)

# Evaluate
results = evaluate_model(
    model=model,
    dataset=dataset,
    generation_mode=True,
    max_new_tokens=128,
    num_beams=4
)

# Print results
print(results)
```

## Interpreting Results

When interpreting evaluation results, consider:

1. **Quantitative Metrics**: BLEU, ROUGE, accuracy, etc.
2. **Qualitative Analysis**: Manual review of generated answers
3. **Error Analysis**: Identify patterns in incorrect answers
4. **Comparison to Baselines**: Compare to simpler models or heuristics

## Next Steps

After evaluation:
- Fine-tune your model based on evaluation results
- Deploy your model for inference
- Explore the example notebooks for more advanced usage
