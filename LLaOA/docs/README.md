# LLaOA Documentation

This directory contains comprehensive documentation for the LLaOA (Large Language and Omics Assistant) framework.

## Documentation Contents

- [Architecture Overview](architecture.md): Detailed explanation of the LLaOA architecture and components
- [Installation Guide](installation.md): Step-by-step instructions for installing LLaOA and its dependencies
- [Data Preparation](data_preparation.md): Guide for preparing omics data and QA pairs for LLaOA
- [Training Guide](training.md): Instructions for training LLaOA models
- [Evaluation Guide](evaluation.md): Methods for evaluating LLaOA models
- [Testing Guide](testing.md): Instructions for running tests for LLaOA
- [Customization Guide](customization.md): How to extend LLaOA with custom components
- [API Reference](api_reference.md): Detailed API documentation for LLaOA components

## Example Notebooks

For interactive examples, see the [examples](../examples/) directory.
