# LLaOA Testing Guide

This document explains how to run tests for the LLaOA framework.

## Overview

LLaOA includes a comprehensive test suite that covers:

1. **Unit Tests**: Tests for individual components like configuration, data processing, etc.
2. **Model Tests**: Tests for model components like the encoder, projector, and model loading/generation

## Running Tests

All tests can be run using the unified test script `run_tests.py` in the root directory.

### Basic Usage

```bash
# Run all tests
python run_tests.py --all

# Run only unit tests
python run_tests.py --unit-tests

# Run only model tests
python run_tests.py --model-tests
```

### Unit Test Options

```bash
# Run a specific unit test module
python run_tests.py --unit-tests --test-module TestConfig

# Run a specific unit test method
python run_tests.py --unit-tests --test-module TestConfig --test-method test_omics_tower_config
```

### Model Test Options

```bash
# Run model tests with a specific COMPASS model
python run_tests.py --model-tests --compass-model-path /path/to/compass/model

# Run model tests with a specific LLaOA model
python run_tests.py --model-tests --model-path /path/to/llaoa/model

# Run model tests with sample data
python run_tests.py --model-tests --sample-data-path /path/to/sample/data

# Test a specific feature type
python run_tests.py --model-tests --feature-type gene_level

# Test a specific projector type
python run_tests.py --model-tests --projector-type mlp2x_gelu

# Test a specific model type
python run_tests.py --model-tests --model-type llama
```

### Output Options

```bash
# Specify output directory for test results
python run_tests.py --all --output-dir ./my_test_results

# Enable verbose output
python run_tests.py --all --verbose
```

## Test Results

Test results are saved in the specified output directory (default: `./test_results`):

- `unit_test_log.txt`: Log file for unit tests
- `unit_test_results.txt`: Results of unit tests
- `model_test_log.txt`: Log file for model tests
- `model_test_results.json`: Results of model tests in JSON format

## Adding New Tests

### Adding Unit Tests

1. Add new test classes or methods to `llaoa/testing/unit_tests.py`
2. Follow the unittest framework conventions

### Adding Model Tests

1. Add new test functions to `llaoa/testing/model_tests.py`
2. Update the `run_all_tests` function to include your new tests
