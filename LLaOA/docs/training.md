# Training Guide for LLaOA

This guide provides detailed instructions for training LLaOA models on omics data.

## Prerequisites

Before training, ensure you have:
- Installed LLaOA and its dependencies (see [Installation Guide](installation.md))
- Prepared your omics data and QA pairs (see [Data Preparation Guide](data_preparation.md))
- Access to a CUDA-compatible GPU (recommended)

## Basic Training

### Command Line Interface

The simplest way to train a LLaOA model is using the `run_train.py` script:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

### Key Parameters

- `--model-base`: Base language model to use (e.g., "meta-llama/Llama-2-7b-hf")
- `--compass-model-path`: Path to the pre-trained COMPASS model
- `--rna-seq-path`: Path to the RNAseq data file
- `--qa-json-path`: Path to the QA pairs JSON file
- `--output-dir`: Directory to save model checkpoints
- `--per-device-train-batch-size`: Batch size per GPU
- `--learning-rate`: Learning rate for optimization
- `--num-train-epochs`: Number of training epochs

## Advanced Training Options

### Parameter-Efficient Fine-Tuning (LoRA)

For more efficient training, especially with limited GPU memory, use LoRA:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_lora" \
    --per-device-train-batch-size 8 \
    --learning-rate 2e-4 \
    --num-train-epochs 5 \
    --lora-rank 16 \
    --lora-alpha 32 \
    --lora-dropout 0.05 \
    --lora-target-modules "q_proj" "k_proj" "v_proj" "o_proj"
```

### Mixed Precision Training

To speed up training and reduce memory usage, enable mixed precision:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_fp16" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3 \
    --fp16
```

### Distributed Training

For multi-GPU training:

```bash
torchrun --nproc_per_node=4 run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_distributed" \
    --per-device-train-batch-size 2 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

## Customizing the Training Process

### Feature Type Selection

Choose the type of features to extract from COMPASS:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --feature-type "concept_level" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_concept" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

Options for `--feature-type`:
- `gene_level`: Raw gene expression features
- `geneset_level`: Gene set enrichment features
- `concept_level`: High-level biological concept features
- `vector`: Raw vector representation

### Projector Type Selection

Choose the type of projector to map omics features to language model space:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --projector-type "mlp3x_gelu" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints_mlp3x" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3
```

Options for `--projector-type`:
- `linear`: Simple linear projection
- `mlp2x_gelu`: Two-layer MLP with GELU activation
- `mlp3x_gelu`: Three-layer MLP with GELU activation

## Monitoring Training

LLaOA logs training progress to the console and saves checkpoints at regular intervals. You can customize the logging and checkpoint frequency:

```bash
python run_train.py \
    --model-base "meta-llama/Llama-2-7b-hf" \
    --compass-model-path "./COMPASS/example/model/pretrainer.pt" \
    --rna-seq-path "COMPASS/example/data/compass_gide_tpm.tsv" \
    --qa-json-path "data/omics_qa.json" \
    --output-dir "./checkpoints" \
    --per-device-train-batch-size 4 \
    --learning-rate 1e-5 \
    --num-train-epochs 3 \
    --logging-steps 5 \
    --eval-steps 50 \
    --save-steps 100
```

## Next Steps

After training your model:
- Evaluate its performance using the [Evaluation Guide](evaluation.md)
- Use it for inference as described in the example notebooks
