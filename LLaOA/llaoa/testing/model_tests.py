"""
Model testing utilities for LLaOA.
"""

import os
import torch
import logging
import pandas as pd
import argparse
from typing import Dict, List, Optional, Union, Any, Tuple

from ..model.config import LlaoaConfig, OmicsTowerConfig
from ..model.builder import load_pretrained_model, identify_model_type
from ..model.omics_encoder.compass_encoder import COMPASSOmicsTower, CompassFeatureType
from ..model.omics_projector.builder import build_omics_projector
from ..model.language_model.llaoa_llama import LlaoaLlamaForCausalLM
from ..model.language_model.llaoa_mistral import LlaoaMistralForCausalLM

# Check if MPT is available
try:
    from ..model.language_model.llaoa_mpt import Lla<PERSON><PERSON>ptF<PERSON><PERSON>ausal<PERSON>, MPT_AVAILABLE
except ImportError:
    MPT_AVAILABLE = False

logger = logging.getLogger(__name__)

def test_compass_encoder(
    model_path: str,
    feature_type: str = "gene_level",
    batch_size: int = 128,
    sample_data_path: Optional[str] = None,
    num_samples: int = 2
) -> bool:
    """
    Test the COMPASS encoder.

    Args:
        model_path: Path to the COMPASS model
        feature_type: Type of features to extract
        batch_size: Batch size for feature extraction
        sample_data_path: Path to sample RNAseq data (if None, create dummy data)
        num_samples: Number of samples to use for testing

    Returns:
        True if test passed, False otherwise
    """
    try:
        # Create encoder config
        encoder_config = {
            'model_path': model_path,
            'feature_type': feature_type,
            'batch_size': batch_size
        }

        # Initialize encoder
        encoder = COMPASSOmicsTower(encoder_config)
        logger.info(f"Initialized COMPASSOmicsTower with {feature_type} features")
        logger.info(f"Hidden size: {encoder.hidden_size}")

        # Load or create sample data
        if sample_data_path is not None:
            # Load sample data
            rna_seq_df = pd.read_csv(sample_data_path, sep='\t')
            logger.info(f"Loaded RNAseq data with shape: {rna_seq_df.shape}")

            # Use a subset of samples
            rna_seq_df = rna_seq_df.iloc[:num_samples]
        else:
            # Create dummy data
            logger.info("Creating dummy RNAseq data")
            if feature_type == "gene_level":
                num_genes = 15672
            elif feature_type == "geneset_level":
                num_genes = 133
            elif feature_type == "concept_level":
                num_genes = 44
            else:
                num_genes = 15672

            # Create dummy DataFrame
            import numpy as np
            data = np.random.rand(num_samples, num_genes)
            rna_seq_df = pd.DataFrame(data)

        # Test forward pass
        with torch.no_grad():
            features = encoder(rna_seq_df)

        logger.info(f"Encoder forward pass successful, output shape: {features.shape}")

        return True
    except Exception as e:
        logger.error(f"Error in COMPASS encoder test: {str(e)}")
        return False

def test_omics_projector(
    input_size: int = 15672,
    output_size: int = 4096,
    projector_type: str = "mlp2x_gelu"
) -> bool:
    """
    Test the omics projector.

    Args:
        input_size: Input size
        output_size: Output size
        projector_type: Type of projector

    Returns:
        True if test passed, False otherwise
    """
    try:
        # Create config
        config = LlaoaConfig(
            omics_tower=OmicsTowerConfig(
                model_path='dummy_path',
                hidden_size=input_size
            ),
            omics_projector_type=projector_type,
            omics_hidden_size=input_size,
            hidden_size=output_size
        )

        # Initialize projector
        projector = build_omics_projector(config)
        logger.info(f"Initialized omics projector with type={projector_type}")

        # Test forward pass
        dummy_input = torch.randn(2, 1, input_size)
        with torch.no_grad():
            projected = projector(dummy_input)

        logger.info(f"Projector forward pass successful, output shape: {projected.shape}")

        return True
    except Exception as e:
        logger.error(f"Error in omics projector test: {str(e)}")
        return False

def test_model_loading(
    model_path: str,
    model_base: Optional[str] = None
) -> bool:
    """
    Test loading a model.

    Args:
        model_path: Path to the model
        model_base: Base model for LoRA

    Returns:
        True if test passed, False otherwise
    """
    try:
        # Load model
        tokenizer, model, _, _ = load_pretrained_model(
            model_path=model_path,
            model_base=model_base
        )

        logger.info(f"Loaded model from {model_path}")
        logger.info(f"Model type: {type(model).__name__}")

        return True
    except Exception as e:
        logger.error(f"Error in model loading test: {str(e)}")
        return False

def test_model_forward_pass(
    model_path: str,
    model_base: Optional[str] = None,
    sample_data_path: Optional[str] = None,
    num_samples: int = 2
) -> bool:
    """
    Test model forward pass.

    Args:
        model_path: Path to the model
        model_base: Base model for LoRA
        sample_data_path: Path to sample RNAseq data (if None, create dummy data)
        num_samples: Number of samples to use for testing

    Returns:
        True if test passed, False otherwise
    """
    try:
        # Load model
        tokenizer, model, _, _ = load_pretrained_model(
            model_path=model_path,
            model_base=model_base
        )

        logger.info(f"Loaded model from {model_path}")

        # Load or create sample data
        if sample_data_path is not None:
            # Load sample data
            rna_seq_df = pd.read_csv(sample_data_path, sep='\t')
            logger.info(f"Loaded RNAseq data with shape: {rna_seq_df.shape}")

            # Use a subset of samples
            rna_seq_df = rna_seq_df.iloc[:num_samples]
        else:
            # Create dummy data
            logger.info("Creating dummy RNAseq data")

            # Get hidden size from model
            if hasattr(model.get_model(), 'omics_tower') and model.get_model().omics_tower is not None:
                num_genes = model.get_model().omics_tower.hidden_size
            else:
                num_genes = 15672

            # Create dummy DataFrame
            import numpy as np
            data = np.random.rand(num_samples, num_genes)
            rna_seq_df = pd.DataFrame(data)

        # Create dummy input IDs
        input_ids = torch.randint(0, 100, (num_samples, 10))

        # Test forward pass
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                omics_data=rna_seq_df
            )

        logger.info(f"Model forward pass successful")

        return True
    except Exception as e:
        logger.error(f"Error in model forward pass test: {str(e)}")
        return False

def test_model_config(model_type: str = "llama") -> bool:
    """
    Test the model configuration for different model types.

    Args:
        model_type: Type of model to test (llama, mistral, mpt)

    Returns:
        True if test passed, False otherwise
    """
    try:
        # Create a config dictionary that resembles a model type
        if model_type == "llama":
            config_dict = {
                "model_type": "llama",
                "hidden_size": 4096,
                "num_attention_heads": 32,
                "num_hidden_layers": 32
            }
        elif model_type == "mistral":
            config_dict = {
                "model_type": "mistral",
                "hidden_size": 4096,
                "num_attention_heads": 32,
                "num_key_value_heads": 8,
                "num_hidden_layers": 32
            }
        elif model_type == "mpt":
            config_dict = {
                "model_type": "mpt",
                "n_embd": 4096,
                "n_heads": 32,
                "n_layers": 32
            }
        else:
            config_dict = {
                "model_type": "unknown",
                "hidden_size": 4096
            }

        # Test model type identification
        identified_type = identify_model_type(config_dict)
        logger.info(f"Model type identification: {identified_type}")

        # Test config creation
        if model_type == "llama":
            from ..model.language_model import LlaoaLlamaConfig
            config = LlaoaLlamaConfig(
                omics_tower=OmicsTowerConfig(
                    model_path='./COMPASS/example/model/pretrainer.pt',
                    feature_type="gene_level"
                ),
                omics_projector_type="mlp2x_gelu",
                omics_hidden_size=15672,
                **config_dict
            )
            logger.info(f"LlaoaLlamaConfig initialized successfully")
        elif model_type == "mistral":
            from ..model.language_model import LlaoaMistralConfig
            config = LlaoaMistralConfig(
                omics_tower=OmicsTowerConfig(
                    model_path='./COMPASS/example/model/pretrainer.pt',
                    feature_type="gene_level"
                ),
                omics_projector_type="mlp2x_gelu",
                omics_hidden_size=15672,
                **config_dict
            )
            logger.info(f"LlaoaMistralConfig initialized successfully")
        elif model_type == "mpt" and MPT_AVAILABLE:
            from ..model.language_model import LlaoaMptConfig
            config = LlaoaMptConfig(
                omics_tower=OmicsTowerConfig(
                    model_path='./COMPASS/example/model/pretrainer.pt',
                    feature_type="gene_level"
                ),
                omics_projector_type="mlp2x_gelu",
                omics_hidden_size=15672,
                **config_dict
            )
            logger.info(f"LlaoaMptConfig initialized successfully")
        else:
            logger.info(f"Skipping config test for {model_type}")

        return True
    except Exception as e:
        logger.error(f"Error in model config test: {str(e)}")
        return False

def test_model_generation(
    model_path: str,
    model_base: Optional[str] = None,
    sample_data_path: Optional[str] = None,
    num_samples: int = 2,
    max_new_tokens: int = 20
) -> bool:
    """
    Test model generation.

    Args:
        model_path: Path to the model
        model_base: Base model for LoRA
        sample_data_path: Path to sample RNAseq data (if None, create dummy data)
        num_samples: Number of samples to use for testing
        max_new_tokens: Maximum number of new tokens to generate

    Returns:
        True if test passed, False otherwise
    """
    try:
        # Load model
        tokenizer, model, _, _ = load_pretrained_model(
            model_path=model_path,
            model_base=model_base
        )

        logger.info(f"Loaded model from {model_path}")

        # Load or create sample data
        if sample_data_path is not None:
            # Load sample data
            rna_seq_df = pd.read_csv(sample_data_path, sep='\t')
            logger.info(f"Loaded RNAseq data with shape: {rna_seq_df.shape}")

            # Use a subset of samples
            rna_seq_df = rna_seq_df.iloc[:num_samples]
        else:
            # Create dummy data
            logger.info("Creating dummy RNAseq data")

            # Get hidden size from model
            if hasattr(model.get_model(), 'omics_tower') and model.get_model().omics_tower is not None:
                num_genes = model.get_model().omics_tower.hidden_size
            else:
                num_genes = 15672

            # Create dummy DataFrame
            import numpy as np
            data = np.random.rand(num_samples, num_genes)
            rna_seq_df = pd.DataFrame(data)

        # Create dummy input IDs
        input_ids = torch.randint(0, 100, (num_samples, 10))

        # Test generation
        with torch.no_grad():
            generated_ids = model.generate(
                inputs=input_ids,
                omics_data=rna_seq_df,
                max_new_tokens=max_new_tokens
            )

        logger.info(f"Model generation successful, output shape: {generated_ids.shape}")

        return True
    except Exception as e:
        logger.error(f"Error in model generation test: {str(e)}")
        return False

def run_all_tests(
    compass_model_path: str,
    model_path: Optional[str] = None,
    model_base: Optional[str] = None,
    sample_data_path: Optional[str] = None
) -> Dict[str, bool]:
    """
    Run all tests.

    Args:
        compass_model_path: Path to the COMPASS model
        model_path: Path to the model (if None, skip model tests)
        model_base: Base model for LoRA
        sample_data_path: Path to sample RNAseq data (if None, create dummy data)

    Returns:
        Dictionary of test results
    """
    results = {}

    # Test COMPASS encoder
    logger.info("Testing COMPASS encoder...")
    results['compass_encoder_gene_level'] = test_compass_encoder(
        model_path=compass_model_path,
        feature_type="gene_level",
        sample_data_path=sample_data_path
    )

    results['compass_encoder_geneset_level'] = test_compass_encoder(
        model_path=compass_model_path,
        feature_type="geneset_level",
        sample_data_path=sample_data_path
    )

    results['compass_encoder_concept_level'] = test_compass_encoder(
        model_path=compass_model_path,
        feature_type="concept_level",
        sample_data_path=sample_data_path
    )

    # Test omics projector
    logger.info("Testing omics projector...")
    results['omics_projector_linear'] = test_omics_projector(
        projector_type="linear"
    )

    results['omics_projector_mlp2x_gelu'] = test_omics_projector(
        projector_type="mlp2x_gelu"
    )

    results['omics_projector_mlp3x_gelu'] = test_omics_projector(
        projector_type="mlp3x_gelu"
    )

    # Test model configs
    logger.info("Testing model configs...")
    results['model_config_llama'] = test_model_config(model_type="llama")
    results['model_config_mistral'] = test_model_config(model_type="mistral")
    if MPT_AVAILABLE:
        results['model_config_mpt'] = test_model_config(model_type="mpt")

    # Test model if provided
    if model_path is not None:
        logger.info("Testing model loading...")
        results['model_loading'] = test_model_loading(
            model_path=model_path,
            model_base=model_base
        )

        logger.info("Testing model forward pass...")
        results['model_forward_pass'] = test_model_forward_pass(
            model_path=model_path,
            model_base=model_base,
            sample_data_path=sample_data_path
        )

        logger.info("Testing model generation...")
        results['model_generation'] = test_model_generation(
            model_path=model_path,
            model_base=model_base,
            sample_data_path=sample_data_path
        )

    # Print results
    logger.info("Test results:")
    for test, result in results.items():
        logger.info(f"{test}: {'PASSED' if result else 'FAILED'}")

    return results

def test_model_components(args):
    """Test the individual components of the LLaOA model."""
    logger.info("Testing LLaOA model components...")

    # Set up logging if not already configured
    if not logger.handlers:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    # Test COMPASS encoder with different feature types
    feature_types = ["gene_level", "geneset_level", "concept_level", "vector"]
    for feature_type in feature_types:
        if args.feature_type and args.feature_type != feature_type:
            continue
        test_compass_encoder(
            model_path=args.compass_model_path,
            feature_type=feature_type,
            sample_data_path=args.sample_data_path
        )

    # Test omics projector with different types
    projector_types = ["linear", "mlp2x_gelu", "mlp3x_gelu"]
    for projector_type in projector_types:
        if args.projector_type and args.projector_type != projector_type:
            continue
        test_omics_projector(projector_type=projector_type)

    # Test model configs for different model types
    model_types = ["llama", "mistral"]
    if MPT_AVAILABLE:
        model_types.append("mpt")

    for model_type in model_types:
        if args.model_type and args.model_type != model_type:
            continue
        test_model_config(model_type)

    # Test model if provided
    if args.model_path:
        test_model_loading(
            model_path=args.model_path,
            model_base=args.model_base
        )

        test_model_forward_pass(
            model_path=args.model_path,
            model_base=args.model_base,
            sample_data_path=args.sample_data_path
        )

        test_model_generation(
            model_path=args.model_path,
            model_base=args.model_base,
            sample_data_path=args.sample_data_path
        )

    logger.info("Component tests completed.")

def parse_args():
    parser = argparse.ArgumentParser(description="Test LLaOA model components")
    parser.add_argument("--compass-model-path", type=str, default="./COMPASS/example/model/pretrainer.pt",
                        help="Path to the COMPASS model")
    parser.add_argument("--model-path", type=str, default=None,
                        help="Path to the LLaOA model")
    parser.add_argument("--model-base", type=str, default=None,
                        help="Base model for LoRA")
    parser.add_argument("--sample-data-path", type=str, default=None,
                        help="Path to sample RNAseq data")
    parser.add_argument("--feature-type", type=str, choices=["gene_level", "geneset_level", "concept_level", "vector"],
                        help="Test specific feature type")
    parser.add_argument("--projector-type", type=str, choices=["linear", "mlp2x_gelu", "mlp3x_gelu"],
                        help="Test specific projector type")
    parser.add_argument("--model-type", type=str, choices=["llama", "mistral", "mpt"],
                        help="Test specific model type")
    parser.add_argument("--run-all", action="store_true",
                        help="Run all tests")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()

    if args.run_all:
        run_all_tests(
            compass_model_path=args.compass_model_path,
            model_path=args.model_path,
            model_base=args.model_base,
            sample_data_path=args.sample_data_path
        )
    else:
        test_model_components(args)
