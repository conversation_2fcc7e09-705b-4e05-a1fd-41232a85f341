"""
Unit tests for LLaOA components.
"""

import os
import unittest
import torch
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any, Tuple

from ..model.config import LlaoaConfig, OmicsTowerConfig
from ..model.omics_encoder.compass_encoder import COMPASSOmicsTower, CompassFeatureType
from ..model.omics_projector.builder import build_omics_projector
from ..data.omics_qa_dataset import OmicsQADataset
from ..data.processing import (
    normalize_rnaseq_data,
    filter_genes,
    augment_rnaseq_data,
    augment_qa_data,
    create_prompt_templates
)

class TestConfig(unittest.TestCase):
    """Tests for configuration classes."""

    def test_omics_tower_config(self):
        """Test OmicsTowerConfig."""
        # Test with default values
        config = OmicsTowerConfig(model_path="dummy_path")
        self.assertEqual(config.model_path, "dummy_path")
        self.assertEqual(config.feature_type, "gene_level")
        self.assertEqual(config.batch_size, 128)
        self.assertEqual(config.hidden_size, 15672)

        # Test with custom values
        config = OmicsTowerConfig(
            model_path="dummy_path",
            feature_type="geneset_level",
            batch_size=64,
            hidden_size=133
        )
        self.assertEqual(config.model_path, "dummy_path")
        self.assertEqual(config.feature_type, "geneset_level")
        self.assertEqual(config.batch_size, 64)
        self.assertEqual(config.hidden_size, 133)

        # Test to_dict
        config_dict = config.to_dict()
        self.assertEqual(config_dict["model_path"], "dummy_path")
        self.assertEqual(config_dict["feature_type"], "geneset_level")
        self.assertEqual(config_dict["batch_size"], 64)
        self.assertEqual(config_dict["hidden_size"], 133)

    def test_llaoa_config(self):
        """Test LlaoaConfig."""
        # Test with default values
        config = LlaoaConfig()
        self.assertEqual(config.model_type, "llaoa")
        self.assertEqual(config.omics_projector_type, "mlp2x_gelu")

        # Test with custom values
        omics_tower = OmicsTowerConfig(model_path="dummy_path")
        config = LlaoaConfig(
            omics_tower=omics_tower,
            omics_projector_type="linear",
            omics_hidden_size=133,
            hidden_size=4096
        )
        self.assertEqual(config.model_type, "llaoa")
        self.assertEqual(config.omics_projector_type, "linear")
        self.assertEqual(config.omics_hidden_size, 133)
        self.assertEqual(config.hidden_size, 4096)
        self.assertEqual(config.omics_tower.model_path, "dummy_path")

        # Test to_dict
        config_dict = config.to_dict()
        self.assertEqual(config_dict["model_type"], "llaoa")
        self.assertEqual(config_dict["omics_projector_type"], "linear")
        self.assertEqual(config_dict["omics_hidden_size"], 133)
        self.assertEqual(config_dict["hidden_size"], 4096)
        self.assertEqual(config_dict["omics_tower"]["model_path"], "dummy_path")

class TestOmicsEncoder(unittest.TestCase):
    """Tests for omics encoder."""

    def test_compass_feature_type_enum(self):
        """Test CompassFeatureType enum."""
        self.assertEqual(CompassFeatureType.GENE_LEVEL.value, "gene_level")
        self.assertEqual(CompassFeatureType.GENESET_LEVEL.value, "geneset_level")
        self.assertEqual(CompassFeatureType.CONCEPT_LEVEL.value, "concept_level")
        self.assertEqual(CompassFeatureType.VECTOR.value, "vector")

        # Test conversion from string
        self.assertEqual(CompassFeatureType("gene_level"), CompassFeatureType.GENE_LEVEL)
        self.assertEqual(CompassFeatureType("geneset_level"), CompassFeatureType.GENESET_LEVEL)
        self.assertEqual(CompassFeatureType("concept_level"), CompassFeatureType.CONCEPT_LEVEL)
        self.assertEqual(CompassFeatureType("vector"), CompassFeatureType.VECTOR)

class TestOmicsProjector(unittest.TestCase):
    """Tests for omics projector."""

    def test_build_omics_projector(self):
        """Test build_omics_projector."""
        # Test linear projector
        config = LlaoaConfig(
            omics_tower=OmicsTowerConfig(
                model_path="dummy_path",
                hidden_size=100
            ),
            omics_projector_type="linear",
            omics_hidden_size=100,
            hidden_size=200
        )
        projector = build_omics_projector(config)
        # For linear projector, check in_features and out_features
        if hasattr(projector, 'in_features'):
            self.assertEqual(projector.in_features, 100)
            self.assertEqual(projector.out_features, 200)

        # Test MLP projector
        config = LlaoaConfig(
            omics_tower=OmicsTowerConfig(
                model_path="dummy_path",
                hidden_size=100
            ),
            omics_projector_type="mlp2x_gelu",
            omics_hidden_size=100,
            hidden_size=200
        )
        projector = build_omics_projector(config)

        # Test forward pass
        dummy_input = torch.randn(2, 1, 100)
        output = projector(dummy_input)
        self.assertEqual(output.shape, (2, 1, 200))

class TestDataProcessing(unittest.TestCase):
    """Tests for data processing."""

    def test_normalize_rnaseq_data(self):
        """Test normalize_rnaseq_data."""
        # Create dummy data
        data = np.random.rand(10, 100)
        df = pd.DataFrame(data)

        # Test standard normalization
        df_norm = normalize_rnaseq_data(df, method="standard", log_transform=False)
        self.assertEqual(df_norm.shape, df.shape)
        self.assertAlmostEqual(df_norm.mean().mean(), 0.0, places=1)
        # Increase tolerance for std check
        self.assertAlmostEqual(df_norm.std().mean(), 1.0, places=0)

        # Test minmax normalization
        df_norm = normalize_rnaseq_data(df, method="minmax", log_transform=False)
        self.assertEqual(df_norm.shape, df.shape)
        self.assertGreaterEqual(df_norm.min().min(), 0.0)
        # Allow for floating point precision issues
        self.assertLessEqual(df_norm.max().max(), 1.0 + 1e-10)

        # Test log transformation
        df_norm = normalize_rnaseq_data(df, method="standard", log_transform=True)
        self.assertEqual(df_norm.shape, df.shape)

    def test_filter_genes(self):
        """Test filter_genes."""
        # Create dummy data
        data = np.random.rand(10, 100)
        df = pd.DataFrame(data)

        # Test min_expression
        df_filtered = filter_genes(df, min_expression=0.5, min_variance=0.0)
        self.assertLessEqual(df_filtered.shape[1], df.shape[1])

        # Test min_variance
        df_filtered = filter_genes(df, min_expression=0.0, min_variance=0.1)
        self.assertLessEqual(df_filtered.shape[1], df.shape[1])

        # Test max_genes
        df_filtered = filter_genes(df, min_expression=0.0, min_variance=0.0, max_genes=50)
        self.assertEqual(df_filtered.shape[1], 50)

    def test_augment_rnaseq_data(self):
        """Test augment_rnaseq_data."""
        # Create dummy data
        data = np.random.rand(10, 100)
        df = pd.DataFrame(data)

        # Test noise augmentation
        df_aug, aug_ids = augment_rnaseq_data(
            df, method="noise", noise_level=0.1, num_augmentations=2
        )
        self.assertEqual(df_aug.shape[0], df.shape[0] * 3)  # Original + 2 augmentations
        self.assertEqual(len(aug_ids), df.shape[0] * 2)

        # Test dropout augmentation
        df_aug, aug_ids = augment_rnaseq_data(
            df, method="dropout", noise_level=0.1, num_augmentations=1
        )
        self.assertEqual(df_aug.shape[0], df.shape[0] * 2)  # Original + 1 augmentation
        self.assertEqual(len(aug_ids), df.shape[0])

        # Test swap augmentation
        df_aug, aug_ids = augment_rnaseq_data(
            df, method="swap", noise_level=0.1, num_augmentations=1
        )
        self.assertEqual(df_aug.shape[0], df.shape[0] * 2)  # Original + 1 augmentation
        self.assertEqual(len(aug_ids), df.shape[0])

    def test_augment_qa_data(self):
        """Test augment_qa_data."""
        # Create dummy QA pairs
        qa_pairs = [
            {"sample_id": "sample1", "question": "Q1", "answer": "A1"},
            {"sample_id": "sample2", "question": "Q2", "answer": "A2"}
        ]

        # Create mapping from original to augmented sample IDs
        original_to_augmented = {
            "sample1": ["sample1_aug1"],
            "sample2": ["sample2_aug1", "sample2_aug2"]
        }

        # Test augmentation
        qa_pairs_aug = augment_qa_data(
            qa_pairs,
            augmented_sample_ids=["sample1_aug1", "sample2_aug1", "sample2_aug2"],
            original_to_augmented=original_to_augmented
        )

        self.assertEqual(len(qa_pairs_aug), 5)  # 2 original + 3 augmented

    def test_create_prompt_templates(self):
        """Test create_prompt_templates."""
        # Create dummy QA pairs
        qa_pairs = [
            {"sample_id": "sample1", "question": "Q1", "answer": "A1"},
            {"sample_id": "sample2", "question": "Q2", "answer": "A2"}
        ]

        # Test prompt templates
        prompt_templates = ["Question: {question}", "{question}", "Q: {question}"]
        answer_prefixes = ["Answer: ", "A: ", ""]

        qa_pairs_var = create_prompt_templates(
            qa_pairs, prompt_templates, answer_prefixes
        )

        self.assertEqual(len(qa_pairs_var), 2)
        self.assertIn("prompt_template", qa_pairs_var[0])
        self.assertIn("answer_prefix", qa_pairs_var[0])

class TestOmicsQADataset(unittest.TestCase):
    """Tests for OmicsQADataset."""

    def test_format_prompt(self):
        """Test format_prompt method."""
        # Create dummy dataset
        class DummyTokenizer:
            def __call__(self, text, truncation=True, max_length=None, return_tensors=None):
                return {"input_ids": torch.randint(0, 100, (1, 10))}

        # Use the dummy files we created
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        dataset = OmicsQADataset(
            rna_seq_path=os.path.join(current_dir, "dummy_rnaseq.csv"),
            qa_json_path=os.path.join(current_dir, "dummy_qa.json"),
            tokenizer=DummyTokenizer(),
            prompt_template="Question: {question}"
        )

        # Test with default template
        formatted = dataset.format_prompt("What is this?")
        self.assertEqual(formatted, "Question: What is this?")

        # Test with custom template
        formatted = dataset.format_prompt("What is this?", template="Q: {question}")
        self.assertEqual(formatted, "Q: What is this?")

        # Test with no template
        formatted = dataset.format_prompt("What is this?", template=None)
        self.assertEqual(formatted, "Question: What is this?")

    def test_format_answer(self):
        """Test format_answer method."""
        # Create dummy dataset
        class DummyTokenizer:
            def __call__(self, text, truncation=True, max_length=None, return_tensors=None):
                return {"input_ids": torch.randint(0, 100, (1, 10))}

        # Use the dummy files we created
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        dataset = OmicsQADataset(
            rna_seq_path=os.path.join(current_dir, "dummy_rnaseq.csv"),
            qa_json_path=os.path.join(current_dir, "dummy_qa.json"),
            tokenizer=DummyTokenizer(),
            answer_prefix="Answer: "
        )

        # Test with default prefix
        formatted = dataset.format_answer("This is an answer.")
        self.assertEqual(formatted, "Answer: This is an answer.")

        # Test with custom prefix
        formatted = dataset.format_answer("This is an answer.", prefix="A: ")
        self.assertEqual(formatted, "A: This is an answer.")

        # Test with no prefix
        formatted = dataset.format_answer("This is an answer.", prefix=None)
        self.assertEqual(formatted, "Answer: This is an answer.")

if __name__ == "__main__":
    unittest.main()
