import torch
import json
import logging
import pandas as pd
from typing import Dict, List, Optional, Union, Any
from torch.utils.data import Dataset

logger = logging.getLogger(__name__)

class OmicsQADataset(Dataset):
    def __init__(
        self,
        rna_seq_path: str,
        qa_json_path: str,
        tokenizer: Any,
        max_length: int = 512,
        sample_id_col: Optional[str] = None,
        include_raw_text: bool = False,
        prompt_template: Optional[str] = None,
        answer_prefix: Optional[str] = None
    ):
        """
        Dataset for omics QA pairs.

        Args:
            rna_seq_path: Path to RNAseq data (TSV/CSV)
            qa_json_path: Path to QA pairs JSON
            tokenizer: Tokenizer for text
            max_length: Maximum sequence length
            sample_id_col: Column name for sample ID in RNAseq data
            include_raw_text: Whether to include raw text in the output
            prompt_template: Template for formatting the prompt (e.g., "Question: {question}")
            answer_prefix: Prefix for the answer (e.g., "Answer: ")
        """
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.include_raw_text = include_raw_text
        self.prompt_template = prompt_template
        self.answer_prefix = answer_prefix
        self.sample_id_col = sample_id_col

        # Load RNAseq data
        logger.info(f"Loading RNAseq data from {rna_seq_path}")
        self.rna_seq_df = pd.read_csv(rna_seq_path, sep='\t' if rna_seq_path.endswith('.tsv') else ',')

        # Set index if sample_id_col is provided
        if sample_id_col:
            # Set sample_id_col as index if present
            self.rna_seq_df.set_index(sample_id_col, inplace=True)

        # Load QA pairs
        logger.info(f"Loading QA pairs from {qa_json_path}")
        with open(qa_json_path, 'r') as f:
            self.qa_pairs = json.load(f)

        logger.info(f"Loaded {len(self.qa_pairs)} QA pairs and {len(self.rna_seq_df)} RNAseq samples")

    def __len__(self):
        return len(self.qa_pairs)

    def format_prompt(self, question: str, template: Optional[str] = None) -> str:
        """
        Format the prompt using the template if provided.

        Args:
            question: The question to format
            template: The template to use (if None, use self.prompt_template)

        Returns:
            Formatted prompt
        """
        template = template or self.prompt_template
        if template:
            return template.format(question=question)
        return question

    def format_answer(self, answer: str, prefix: Optional[str] = None) -> str:
        """
        Format the answer with the prefix if provided.

        Args:
            answer: The answer to format
            prefix: The prefix to use (if None, use self.answer_prefix)

        Returns:
            Formatted answer
        """
        prefix = prefix or self.answer_prefix
        if prefix:
            return f"{prefix}{answer}"
        return answer

    def __getitem__(self, idx):
        qa = self.qa_pairs[idx]
        sample_id = qa['sample_id']

        # Get prompt template and answer prefix from QA pair if available
        prompt_template = qa.get('prompt_template', self.prompt_template)
        answer_prefix = qa.get('answer_prefix', self.answer_prefix)

        # Format question and answer
        question = self.format_prompt(qa['question'], prompt_template)
        answer = self.format_answer(qa['answer'], answer_prefix)

        # Get RNAseq vector for this sample (row as DataFrame)
        try:
            row = self.rna_seq_df.loc[[sample_id]]
        except KeyError:
            raise KeyError(f"Sample ID {sample_id} not found in RNAseq data. "
                           f"Available sample IDs: {list(self.rna_seq_df.index)[:5]}...")

        # Tokenize question and answer
        question_ids = self.tokenizer(
            question,
            truncation=True,
            max_length=self.max_length,
            return_tensors='pt'
        )

        answer_ids = self.tokenizer(
            answer,
            truncation=True,
            max_length=self.max_length,
            return_tensors='pt'
        )

        result = {
            'omics': row,  # DataFrame with one row, for COMPASSOmicsTower
            'question_ids': question_ids['input_ids'].squeeze(0),
            'answer_ids': answer_ids['input_ids'].squeeze(0)
        }

        # Include raw text if requested
        if self.include_raw_text:
            result.update({
                'sample_id': sample_id,
                'question': question,
                'answer': answer,
                'original_question': qa['question'],
                'original_answer': qa['answer']
            })

        return result

    @classmethod
    def from_config(cls, config: Dict[str, Any], tokenizer: Any) -> 'OmicsQADataset':
        """Create a dataset from a configuration dictionary."""
        return cls(
            rna_seq_path=config['rna_seq_path'],
            qa_json_path=config['qa_json_path'],
            tokenizer=tokenizer,
            max_length=config.get('max_length', 512),
            sample_id_col=config.get('sample_id_col'),
            include_raw_text=config.get('include_raw_text', False),
            prompt_template=config.get('prompt_template'),
            answer_prefix=config.get('answer_prefix')
        )