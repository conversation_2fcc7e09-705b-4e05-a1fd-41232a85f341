import os
import torch
import argparse
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from torch.utils.data import DataLoader
from transformers import AutoTokenizer, HfArgumentParser
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from rouge_score import rouge_scorer
from accelerate import Accelerator
from accelerate.logging import get_logger

from llaoa.model.builder import load_pretrained_model
from llaoa.data.omics_qa_dataset import OmicsQADataset

# Set up logging
logger = get_logger(__name__)

# Try to import nltk for BLEU score
try:
    import nltk
    nltk.download('punkt', quiet=True)
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False
    logger.warning("NLTK not installed, BLEU score will not be available")

# Try to import rouge for ROUGE score
try:
    from rouge_score import rouge_scorer
    ROUGE_AVAILABLE = True
except ImportError:
    ROUGE_AVAILABLE = False
    logger.warning("rouge_score not installed, ROUGE score will not be available")

@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to evaluate.
    """
    model_path: str
    model_base: Optional[str] = None

@dataclass
class DataArguments:
    """
    Arguments pertaining to what data we are going to input our model for evaluation.
    """
    rna_seq_path: str = None  # Will be resolved at runtime
    qa_json_path: str = None  # Will be resolved at runtime
    sample_id_col: Optional[str] = None
    max_length: int = 512

    def __post_init__(self):
        # Resolve RNA-seq path if not explicitly provided
        if self.rna_seq_path is None:
            from pathlib import Path
            # Try multiple possible locations
            possible_paths = [
                "COMPASS/example/data/compass_gide_tpm.tsv",
                "../COMPASS/example/data/compass_gide_tpm.tsv",
                str(Path(__file__).parents[2] / "COMPASS/example/data/compass_gide_tpm.tsv"),
                str(Path(__file__).parents[3] / "COMPASS/example/data/compass_gide_tpm.tsv")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.rna_seq_path = path
                    break
            if self.rna_seq_path is None:
                self.rna_seq_path = "COMPASS/example/data/compass_gide_tpm.tsv"  # Default fallback

        # Resolve QA JSON path if not explicitly provided
        if self.qa_json_path is None:
            from pathlib import Path
            # Try multiple possible locations
            possible_paths = [
                "data/omics_qa.json",
                "../data/omics_qa.json",
                str(Path(__file__).parents[2] / "data/omics_qa.json"),
                str(Path(__file__).parents[3] / "data/omics_qa.json")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.qa_json_path = path
                    break
            if self.qa_json_path is None:
                self.qa_json_path = "data/omics_qa.json"  # Default fallback

@dataclass
class EvalArguments:
    """
    Arguments pertaining to evaluation.
    """
    output_dir: str = "./eval_results"
    per_device_eval_batch_size: int = 4
    generation_mode: bool = False
    max_new_tokens: int = 128
    num_beams: int = 1
    do_sample: bool = False
    temperature: float = 1.0
    top_p: float = 1.0
    top_k: int = 50
    repetition_penalty: float = 1.0
    length_penalty: float = 1.0
    no_repeat_ngram_size: int = 0
    fp16: bool = False
    bf16: bool = False
    dataloader_num_workers: int = 4
    seed: int = 42

def omics_collate_fn(batch):
    """
    Collate function for batching omics data and QA pairs.

    Args:
        batch: List of dictionaries with 'omics', 'question_ids', and 'answer_ids'

    Returns:
        Dictionary with batched data
    """
    # Collate omics DataFrame rows into a single DataFrame
    omics_list = [item['omics'] for item in batch]
    omics_df = pd.concat(omics_list, axis=0)

    # Pad sequences for question and answer IDs
    question_ids = torch.nn.utils.rnn.pad_sequence(
        [item['question_ids'] for item in batch],
        batch_first=True,
        padding_value=0
    )
    answer_ids = torch.nn.utils.rnn.pad_sequence(
        [item['answer_ids'] for item in batch],
        batch_first=True,
        padding_value=0
    )

    # Include original questions and answers for evaluation
    questions = [item['question'] for item in batch]
    answers = [item['answer'] for item in batch]
    sample_ids = [item['sample_id'] for item in batch]

    return {
        'omics': omics_df,
        'question_ids': question_ids,
        'answer_ids': answer_ids,
        'questions': questions,
        'answers': answers,
        'sample_ids': sample_ids
    }

def compute_metrics(
    generated_texts: List[str],
    reference_texts: List[str],
    questions: List[str],
    sample_ids: List[str],
    output_dir: str,
    model_name: str = "LLaOA Model",
    create_visualizations: bool = True
) -> Dict[str, float]:
    """
    Compute evaluation metrics for generated text and create visualizations.

    Args:
        generated_texts: List of generated texts
        reference_texts: List of reference texts
        questions: List of questions
        sample_ids: List of sample IDs
        output_dir: Directory to save visualizations
        model_name: Name of the model
        create_visualizations: Whether to create visualizations

    Returns:
        Dictionary of metrics
    """
    from .metrics import compute_metrics_for_batch

    # Compute metrics
    results = compute_metrics_for_batch(
        predictions=generated_texts,
        references=reference_texts,
        include_per_sample=True
    )

    # Extract per-sample metrics
    per_sample_metrics = {}
    for key in list(results.keys()):
        if key.endswith('_per_sample'):
            base_key = key[:-11]  # Remove '_per_sample'
            per_sample_metrics[base_key] = results.pop(key)

    # Create visualizations if requested
    if create_visualizations:
        try:
            from .visualization import (
                plot_generation_length_distribution,
                generate_word_clouds,
                plot_metrics_by_sample,
                create_html_report
            )

            # Create visualizations directory
            viz_dir = os.path.join(output_dir, 'visualizations')
            os.makedirs(viz_dir, exist_ok=True)

            # Plot text length distribution
            plot_generation_length_distribution(
                generated_texts=generated_texts,
                reference_texts=reference_texts,
                output_dir=viz_dir,
                title=f"Text Length Distribution - {model_name}"
            )

            # Generate word clouds
            generate_word_clouds(
                generated_texts=generated_texts,
                reference_texts=reference_texts,
                output_dir=viz_dir,
                title=f"Word Clouds - {model_name}"
            )

            # Plot metrics by sample
            plot_metrics_by_sample(
                metrics_by_sample=per_sample_metrics,
                sample_ids=sample_ids,
                output_dir=viz_dir,
                title=f"Metrics by Sample - {model_name}"
            )

            # Create HTML report
            create_html_report(
                results=results,
                generated_texts=generated_texts,
                reference_texts=reference_texts,
                questions=questions,
                sample_ids=sample_ids,
                output_dir=output_dir,
                model_name=model_name
            )
        except ImportError as e:
            logger.warning(f"Could not create visualizations: {str(e)}")

    return results

def evaluate():
    """Main evaluation function."""
    # Parse arguments
    parser = HfArgumentParser((ModelArguments, DataArguments, EvalArguments))
    model_args, data_args, eval_args = parser.parse_args_into_dataclasses()

    # Set up accelerator
    accelerator = Accelerator(
        mixed_precision=eval_args.fp16 and "fp16" or eval_args.bf16 and "bf16" or "no"
    )

    # Create output directory if it doesn't exist
    if accelerator.is_main_process:
        os.makedirs(eval_args.output_dir, exist_ok=True)

    # Load model and tokenizer
    logger.info(f"Loading model from {model_args.model_path}")
    tokenizer, model, _, _ = load_pretrained_model(
        model_path=model_args.model_path,
        model_base=model_args.model_base
    )

    # Load dataset
    logger.info(f"Loading dataset from {data_args.qa_json_path} and {data_args.rna_seq_path}")
    dataset = OmicsQADataset(
        rna_seq_path=data_args.rna_seq_path,
        qa_json_path=data_args.qa_json_path,
        tokenizer=tokenizer,
        max_length=data_args.max_length,
        sample_id_col=data_args.sample_id_col,
        include_raw_text=True  # Include original questions and answers
    )

    # Create dataloader
    dataloader = DataLoader(
        dataset,
        batch_size=eval_args.per_device_eval_batch_size,
        shuffle=False,
        collate_fn=omics_collate_fn,
        num_workers=eval_args.dataloader_num_workers
    )

    # Prepare model and dataloader with accelerator
    model, dataloader = accelerator.prepare(model, dataloader)

    # Set model to evaluation mode
    model.eval()

    # Prepare for evaluation
    all_predictions = []
    all_labels = []
    all_generated_texts = []
    all_reference_texts = []
    all_questions = []
    all_sample_ids = []

    # Log info
    logger.info("***** Running evaluation *****")
    logger.info(f"  Num examples = {len(dataset)}")
    logger.info(f"  Batch size per device = {eval_args.per_device_eval_batch_size}")
    logger.info(f"  Generation mode = {eval_args.generation_mode}")

    # Evaluate
    with torch.no_grad():
        for batch in dataloader:
            omics = batch['omics']  # DataFrame
            question_ids = batch['question_ids']
            answer_ids = batch['answer_ids']
            questions = batch['questions']
            answers = batch['answers']
            sample_ids = batch['sample_ids']

            if eval_args.generation_mode:
                # Generate text
                generation_kwargs = {
                    "max_new_tokens": eval_args.max_new_tokens,
                    "do_sample": eval_args.do_sample,
                    "num_beams": eval_args.num_beams,
                    "temperature": eval_args.temperature,
                    "top_p": eval_args.top_p,
                    "top_k": eval_args.top_k,
                    "repetition_penalty": eval_args.repetition_penalty,
                    "length_penalty": eval_args.length_penalty,
                    "no_repeat_ngram_size": eval_args.no_repeat_ngram_size,
                }

                generated_ids = model.generate(
                    inputs=question_ids,
                    omics_data=omics,
                    **generation_kwargs
                )

                # Decode generated text and reference text
                generated_texts = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)
                reference_texts = answers

                all_generated_texts.extend(generated_texts)
                all_reference_texts.extend(reference_texts)
                all_questions.extend(questions)
                all_sample_ids.extend(sample_ids)
            else:
                # Forward pass for classification
                outputs = model(
                    input_ids=question_ids,
                    omics_data=omics,
                    labels=answer_ids
                )

                # Get predictions
                logits = outputs.logits if hasattr(outputs, 'logits') else outputs[1]
                predictions = torch.argmax(logits, dim=-1)

                # Get labels (shift to match predictions)
                labels = answer_ids[:, 1:].contiguous()
                predictions = predictions[:, :-1].contiguous()

                # Mask out padding tokens
                mask = (labels != tokenizer.pad_token_id).float()

                # Flatten and filter
                flat_predictions = predictions.view(-1)
                flat_labels = labels.view(-1)
                flat_mask = mask.view(-1)

                # Keep only non-padding tokens
                valid_indices = torch.nonzero(flat_mask).squeeze()
                valid_predictions = flat_predictions[valid_indices]
                valid_labels = flat_labels[valid_indices]

                all_predictions.extend(valid_predictions.cpu().numpy())
                all_labels.extend(valid_labels.cpu().numpy())

    # Compute metrics
    results = {}

    if eval_args.generation_mode:
        # For generation mode, compute text-based metrics
        results = compute_metrics(
            generated_texts=all_generated_texts,
            reference_texts=all_reference_texts,
            questions=all_questions,
            sample_ids=all_sample_ids,
            output_dir=eval_args.output_dir,
            model_name=os.path.basename(model_args.model_path),
            create_visualizations=True
        )

        # Save generated texts
        if accelerator.is_main_process:
            with open(os.path.join(eval_args.output_dir, 'generated_texts.txt'), 'w') as f:
                for sample_id, question, gen, ref in zip(all_sample_ids, all_questions, all_generated_texts, all_reference_texts):
                    f.write(f"Sample ID: {sample_id}\n")
                    f.write(f"Question: {question}\n")
                    f.write(f"Generated: {gen}\n")
                    f.write(f"Reference: {ref}\n")
                    f.write("-" * 80 + "\n")

            # Save as CSV for easier analysis
            import csv
            with open(os.path.join(eval_args.output_dir, 'generated_texts.csv'), 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['sample_id', 'question', 'generated', 'reference'])
                for sample_id, question, gen, ref in zip(all_sample_ids, all_questions, all_generated_texts, all_reference_texts):
                    writer.writerow([sample_id, question, gen, ref])
    else:
        # For classification mode, compute standard metrics
        results['accuracy'] = accuracy_score(all_labels, all_predictions)
        results['f1'] = f1_score(all_labels, all_predictions, average='macro')
        results['precision'] = precision_score(all_labels, all_predictions, average='macro')
        results['recall'] = recall_score(all_labels, all_predictions, average='macro')

        # Create confusion matrix if possible
        if accelerator.is_main_process:
            try:
                from .visualization import plot_confusion_matrix

                # Create visualizations directory
                viz_dir = os.path.join(eval_args.output_dir, 'visualizations')
                os.makedirs(viz_dir, exist_ok=True)

                # Plot confusion matrix
                plot_confusion_matrix(
                    true_labels=all_labels,
                    pred_labels=all_predictions,
                    output_dir=viz_dir,
                    title=f"Confusion Matrix - {os.path.basename(model_args.model_path)}"
                )
            except ImportError as e:
                logger.warning(f"Could not create confusion matrix: {str(e)}")

    # Print results
    logger.info("Evaluation Results:")
    for metric, value in results.items():
        logger.info(f"{metric}: {value:.4f}")

    # Save results
    if accelerator.is_main_process:
        with open(os.path.join(eval_args.output_dir, 'results.txt'), 'w') as f:
            for metric, value in results.items():
                f.write(f"{metric}: {value:.4f}\n")

        # Save as JSON for easier parsing
        import json
        with open(os.path.join(eval_args.output_dir, 'results.json'), 'w') as f:
            json.dump({k: float(v) for k, v in results.items()}, f, indent=2)

        # Create comparison plot if previous results exist
        try:
            from .visualization import plot_metrics_comparison

            # Find previous results
            results_paths = []
            for root, dirs, files in os.walk(os.path.dirname(eval_args.output_dir)):
                for file in files:
                    if file == 'results.json':
                        results_paths.append(os.path.join(root, file))

            # If we have multiple results, create comparison plot
            if len(results_paths) > 1:
                plot_metrics_comparison(
                    results_paths=results_paths,
                    output_dir=os.path.join(eval_args.output_dir, 'visualizations'),
                    title="Model Comparison"
                )
        except ImportError as e:
            logger.warning(f"Could not create comparison plot: {str(e)}")

    return results

if __name__ == '__main__':
    evaluate()