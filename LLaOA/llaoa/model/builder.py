import os
import torch
import transformers
import logging
from typing import Op<PERSON>, Dict, <PERSON>, <PERSON><PERSON>, Union
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoConfig

from .config import LlaoaConfig, OmicsTowerConfig
from .omics_encoder.builder import build_omics_tower
from .omics_projector.builder import build_omics_projector
from .language_model import (
    LlaoaLlamaForCausalLM, LlaoaLlamaConfig,
    LlaoaMistralForCausalLM, LlaoaMistralConfig
)

# Check if MPT is available
try:
    from .language_model import LlaoaMptForCausalLM, LlaoaMptConfig, MPT_AVAILABLE
except ImportError:
    MPT_AVAILABLE = False

# Set up logging
logger = logging.getLogger(__name__)

def get_model_name_from_path(model_path: str) -> str:
    """
    Extract model name from path.

    Args:
        model_path: Path to the model or model name

    Returns:
        Model name extracted from path
    """
    if not model_path:
        return ""

    # Extract model name from path
    model_path = model_path.strip("/")
    if "/" in model_path:
        return model_path.split("/")[-1]
    else:
        return model_path

def identify_model_type(config: Dict[str, Any]) -> str:
    """
    Identify the type of language model from config.

    Args:
        config: Model configuration dictionary

    Returns:
        Model type string: "llama", "mistral", "mpt", or "unknown"
    """
    model_type = config.get("model_type", "").lower()

    if "llama" in model_type:
        return "llama"
    elif "mistral" in model_type:
        return "mistral"
    elif "mpt" in model_type:
        return "mpt"
    else:
        # Try to infer from other config attributes
        if "num_key_value_heads" in config and "num_attention_heads" in config:
            # Mistral has grouped query attention
            return "mistral"
        elif "n_heads" in config and "n_embd" in config:
            # MPT uses these parameter names
            return "mpt"
        elif "hidden_size" in config and "num_attention_heads" in config:
            # Llama uses these parameter names
            return "llama"

        return "unknown"

def load_pretrained_model(
    model_path: str,
    model_base: Optional[str] = None,
    model_name: Optional[str] = None,
    device_map: Union[str, Dict[str, Union[int, str]]] = "auto",
    **kwargs
) -> Tuple[Any, Any, None, int]:
    """
    Load a pretrained LLaOA model.

    Args:
        model_path: Path to the model or model name
        model_base: Base model path/name if using LoRA
        model_name: Optional model name for configuration
        device_map: Device mapping strategy

    Returns:
        tokenizer, model, omics_processor, context_len
    """
    # Load tokenizer
    if model_base is not None:
        # LoRA case: load base model tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_base, use_fast=False)
    else:
        # Regular case: load from model path
        tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)

    # Add special tokens if needed
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Load configuration
    if model_name is None:
        model_name = get_model_name_from_path(model_path)

    # Create LLaOA config
    if model_base is not None:
        # LoRA case: load base model config
        base_config = AutoConfig.from_pretrained(model_base)

        # Identify model type
        model_type = identify_model_type(base_config.to_dict())
        logger.info(f"Identified model type from base model: {model_type}")

        # Create appropriate config based on model type
        if model_type == "llama":
            config_class = LlaoaLlamaConfig
        elif model_type == "mistral":
            config_class = LlaoaMistralConfig
        elif model_type == "mpt" and MPT_AVAILABLE:
            config_class = LlaoaMptConfig
        else:
            # Default to Llama config
            logger.warning(f"Unknown model type: {model_type}, defaulting to Llama")
            config_class = LlaoaLlamaConfig

        # Create config with base model values
        config = config_class(
            omics_tower=OmicsTowerConfig(
                model_path=os.path.join(model_path, "compass_model"),
                feature_type="gene_level",
                batch_size=128
            ),
            omics_projector_type="mlp2x_gelu",
            omics_hidden_size=15672,
            **{k: v for k, v in base_config.to_dict().items()
               if k not in ["omics_tower", "omics_projector_type", "omics_hidden_size"]}
        )
    else:
        # Regular case: load from model path
        try:
            # Try to load LLaOA config directly
            config = AutoConfig.from_pretrained(model_path)

            # Check if it's a LLaOA config
            if not any(hasattr(config, attr) for attr in ["omics_tower", "omics_projector_type"]):
                # Not a LLaOA config, create one
                raise ValueError("Not a LLaOA config")

        except Exception as e:
            logger.info(f"Creating new LLaOA config: {str(e)}")

            # Load base config
            base_config = AutoConfig.from_pretrained(model_path)

            # Identify model type
            model_type = identify_model_type(base_config.to_dict())
            logger.info(f"Identified model type: {model_type}")

            # Create appropriate config based on model type
            if model_type == "llama":
                config_class = LlaoaLlamaConfig
            elif model_type == "mistral":
                config_class = LlaoaMistralConfig
            elif model_type == "mpt" and MPT_AVAILABLE:
                config_class = LlaoaMptConfig
            else:
                # Default to Llama config
                logger.warning(f"Unknown model type: {model_type}, defaulting to Llama")
                config_class = LlaoaLlamaConfig

            # Create config with base model values
            config = config_class(
                omics_tower=OmicsTowerConfig(
                    model_path=os.path.join(model_path, "compass_model"),
                    feature_type="gene_level",
                    batch_size=128
                ),
                omics_projector_type="mlp2x_gelu",
                omics_hidden_size=15672,
                **{k: v for k, v in base_config.to_dict().items()
                   if k not in ["omics_tower", "omics_projector_type", "omics_hidden_size"]}
            )

    # Load model
    if model_base is not None:
        # LoRA case
        from peft import PeftModel

        # Load base model
        base_model = AutoModelForCausalLM.from_pretrained(
            model_base,
            config=config,
            torch_dtype=torch.float16,
            device_map=device_map,
            **kwargs
        )

        # Load LoRA model
        model = PeftModel.from_pretrained(base_model, model_path)
    else:
        # Regular case
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            config=config,
            torch_dtype=torch.float16,
            device_map=device_map,
            **kwargs
        )

    # Wrap model with appropriate LLaOA architecture based on model type
    model_type = identify_model_type(config.to_dict())

    if model_type == "llama":
        model = LlaoaLlamaForCausalLM(model)
    elif model_type == "mistral":
        model = LlaoaMistralForCausalLM(model)
    elif model_type == "mpt" and MPT_AVAILABLE:
        model = LlaoaMptForCausalLM(model)
    else:
        # Default to Llama
        logger.warning(f"Unknown model type for wrapping: {model_type}, defaulting to Llama")
        model = LlaoaLlamaForCausalLM(model)

    # Load omics tower
    if hasattr(config, "omics_tower") and config.omics_tower is not None:
        omics_tower = build_omics_tower(config.omics_tower)
        model.get_model().omics_tower = omics_tower

        # Load omics projector
        omics_projector = build_omics_projector(config)
        model.get_model().omics_projector = omics_projector

    # Get context length
    context_len = getattr(config, "max_position_embeddings", 2048)

    # No specific omics processor needed, return None
    omics_processor = None

    return tokenizer, model, omics_processor, context_len
