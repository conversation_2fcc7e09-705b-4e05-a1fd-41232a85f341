import torch
import torch.nn as nn
import pandas as pd
import sys
from enum import Enum
from typing import Optional, Union, Dict, Any, Tuple
from pathlib import Path

# Add COMPASS to the Python path if needed
# Try multiple possible locations for COMPASS
possible_compass_paths = [
    # When running from LLaOA/ directory
    str(Path(__file__).parents[3] / 'COMPASS'),
    # When running from within the package
    str(Path(__file__).parents[3].parent / 'COMPASS'),
    # Fallback to relative path
    './COMPASS'
]

# Add the first valid COMPASS path to sys.path
for compass_path in possible_compass_paths:
    if Path(compass_path).exists():
        if compass_path not in sys.path:
            sys.path.insert(0, compass_path)
        break

# Import COMPASS components
try:
    import compass
    from compass import loadcompass, PreTrainer, FineTuner
except ImportError:
    raise ImportError(
        "Could not import COMPASS. Make sure it's installed and in your Python path. "
        "Tried the following paths: {}".format(possible_compass_paths)
    )

class CompassFeatureType(Enum):
    """Types of features that can be extracted from COMPASS."""
    GENE_LEVEL = "gene_level"  # 15,672 gene-level features
    GENESET_LEVEL = "geneset_level"  # 133 geneset-level features
    CONCEPT_LEVEL = "concept_level"  # 44 concept-level features
    VECTOR = "vector"  # 32-dimensional vector features

class COMPASSOmicsTower(nn.Module):
    """
    Wrapper for COMPASS model to use as an omics encoder in LLaOA.

    This class wraps a COMPASS model and provides methods to extract different types
    of features from RNAseq data.
    """

    def __init__(
        self,
        encoder_config: Dict[str, Any],
        device: Optional[Union[str, torch.device]] = None,
        dtype: Optional[torch.dtype] = None
    ):
        """
        Initialize the COMPASS omics tower.

        Args:
            encoder_config: Configuration dictionary with the following keys:
                - model_path: Path to the COMPASS model
                - feature_type: Type of features to extract (default: "gene_level")
                - batch_size: Batch size for feature extraction (default: 128)
                - hidden_size: Hidden size of the output features (default: depends on feature_type)
            device: Device to use for computation
            dtype: Data type to use for computation
        """
        super().__init__()

        # Get configuration parameters
        model_path = encoder_config.get('model_path', None)
        if model_path is None:
            raise ValueError('model_path must be provided in encoder_config for COMPASSOmicsTower')

        # Determine feature type
        feature_type_str = encoder_config.get('feature_type', 'gene_level')
        try:
            self.feature_type = CompassFeatureType(feature_type_str)
        except ValueError:
            valid_types = [t.value for t in CompassFeatureType]
            raise ValueError(f"Invalid feature_type: {feature_type_str}. Must be one of {valid_types}")

        # Set batch size for feature extraction
        self.batch_size = encoder_config.get('batch_size', 128)

        # Load COMPASS model
        self.model = loadcompass(model_path, map_location='cpu' if device is None else device)

        # Set hidden size based on feature type
        if self.feature_type == CompassFeatureType.GENE_LEVEL:
            self.hidden_size = encoder_config.get('hidden_size', 15672)  # 15,672 genes
        elif self.feature_type == CompassFeatureType.GENESET_LEVEL:
            self.hidden_size = encoder_config.get('hidden_size', 133)  # 133 genesets
        elif self.feature_type == CompassFeatureType.CONCEPT_LEVEL:
            self.hidden_size = encoder_config.get('hidden_size', 44)  # 44 concepts
        elif self.feature_type == CompassFeatureType.VECTOR:
            self.hidden_size = encoder_config.get('hidden_size', 32)  # 32-dim vectors

        # Set device and dtype
        self._device = device or torch.device('cpu')
        self._dtype = dtype or torch.float32

        # Set model to eval mode
        self.model.eval()

    def forward(self, rna_seq_data: pd.DataFrame) -> torch.Tensor:
        """
        Extract features from RNAseq data using COMPASS.

        Args:
            rna_seq_data: pandas DataFrame with RNAseq data, shape [batch_size, num_genes]

        Returns:
            torch.Tensor: Extracted features, shape depends on feature_type:
                - GENE_LEVEL: [batch_size, 1, 15672]
                - GENESET_LEVEL: [batch_size, 1, 133]
                - CONCEPT_LEVEL: [batch_size, 1, 44]
                - VECTOR: [batch_size, 1, 32]
        """
        if not isinstance(rna_seq_data, pd.DataFrame):
            raise ValueError('rna_seq_data must be a pandas DataFrame')

        # Extract features based on feature type
        if self.feature_type == CompassFeatureType.GENE_LEVEL:
            # Get gene-level features (scalar scores)
            gene_features, _, _ = self.model.extract(
                rna_seq_data,
                batch_size=self.batch_size,
                with_gene_level=True
            )
            features_df = gene_features

        elif self.feature_type == CompassFeatureType.GENESET_LEVEL:
            # Get geneset-level features (scalar scores)
            _, geneset_features, _ = self.model.extract(
                rna_seq_data,
                batch_size=self.batch_size,
                with_gene_level=False
            )
            features_df = geneset_features

        elif self.feature_type == CompassFeatureType.CONCEPT_LEVEL:
            # Get concept-level features (scalar scores)
            _, _, concept_features = self.model.extract(
                rna_seq_data,
                batch_size=self.batch_size,
                with_gene_level=False
            )
            features_df = concept_features

        elif self.feature_type == CompassFeatureType.VECTOR:
            # Get vector features (32-dim vectors)
            geneset_vectors, concept_vectors = self.model.project(
                rna_seq_data,
                batch_size=self.batch_size
            )
            # Use concept vectors by default (more compact)
            features_df = concept_vectors

        # Convert DataFrame to tensor
        features_tensor = torch.tensor(features_df.values, device=self.device, dtype=self.dtype)

        # Add sequence dimension for compatibility: [batch, 1, features]
        features_tensor = features_tensor.unsqueeze(1)

        return features_tensor

    @property
    def device(self) -> torch.device:
        """Get the device used by the model."""
        return self._device

    @property
    def dtype(self) -> torch.dtype:
        """Get the data type used by the model."""
        return self._dtype