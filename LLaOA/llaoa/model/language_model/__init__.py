"""Language model integrations for LLaOA."""

# Llama integration
from .llaoa_llama import LlaoaLlamaForCausalLM, LlaoaLlamaConfig, LlaoaLlamaModel

# Mistral integration
from .llaoa_mistral import LlaoaMistralForCausalLM, LlaoaMistralConfig, LlaoaMistralModel

# MPT integration (conditional)
try:
    from .llaoa_mpt import LlaoaMptForCausalLM, LlaoaMptConfig, LlaoaMptModel
    MPT_AVAILABLE = True
except ImportError:
    MPT_AVAILABLE = False
