import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple, Union

from .omics_encoder.builder import build_omics_tower
from .omics_projector.builder import build_omics_projector

class LlaoaMetaModel(ABC):
    """
    Base class for LLaOA models that integrates omics encoder with language models.
    """

    def __init__(self, config):
        super(LlaoaMetaModel, self).__init__(config)

        if hasattr(config, "omics_tower"):
            self.omics_tower = build_omics_tower(config.omics_tower)
            self.mm_projector = build_omics_projector(config)

            # For compatibility with different projector types
            self.omics_projector = self.mm_projector

    def get_omics_tower(self):
        """Get the omics tower (encoder) module."""
        omics_tower = getattr(self, 'omics_tower', None)
        if type(omics_tower) is list:
            omics_tower = omics_tower[0]
        return omics_tower

    def encode_omics(self, omics_data):
        """
        Encode omics data using the omics tower and project to language model dimension.

        Args:
            omics_data: pandas DataFrame with RNAseq data

        Returns:
            torch.Tensor: Projected omics features
        """
        omics_features = self.get_omics_tower()(omics_data)
        omics_features = self.mm_projector(omics_features)
        return omics_features

    def prepare_inputs_labels_for_multimodal(
        self, input_ids, position_ids, attention_mask, past_key_values, labels, omics_data
    ):
        """
        Prepare inputs for the language model by incorporating omics features.

        Args:
            input_ids: Input token IDs
            position_ids: Position IDs
            attention_mask: Attention mask
            past_key_values: Past key values for efficient generation
            labels: Labels for computing loss
            omics_data: RNAseq data as pandas DataFrame

        Returns:
            Tuple of processed inputs for the language model
        """
        omics_tower = self.get_omics_tower()
        if omics_tower is None or omics_data is None or input_ids is None or input_ids.shape[1] == 1:
            return input_ids, position_ids, attention_mask, past_key_values, None, labels

        # Encode omics data
        omics_features = self.encode_omics(omics_data)

        # Create inputs_embeds by concatenating token embeddings with omics features
        inputs_embeds = self.embed_tokens(input_ids)

        # Concatenate omics features with the first token embedding
        # This is a simplified approach - in a real implementation, you might want to use
        # special tokens to mark the beginning and end of omics data
        inputs_embeds = torch.cat([omics_features.to(inputs_embeds.device), inputs_embeds], dim=1)

        # Adjust attention_mask and position_ids to account for the added omics features
        if attention_mask is not None:
            omics_attention_mask = torch.ones(
                (attention_mask.shape[0], omics_features.shape[1]),
                dtype=attention_mask.dtype,
                device=attention_mask.device
            )
            attention_mask = torch.cat([omics_attention_mask, attention_mask], dim=1)

        if position_ids is not None:
            omics_position_ids = torch.zeros(
                (position_ids.shape[0], omics_features.shape[1]),
                dtype=position_ids.dtype,
                device=position_ids.device
            )
            position_ids = torch.cat([omics_position_ids, position_ids], dim=1)

        if labels is not None:
            # Add -100 labels for omics tokens (they shouldn't contribute to loss)
            omics_labels = torch.full(
                (labels.shape[0], omics_features.shape[1]),
                -100,  # -100 is the standard ignore index for CrossEntropyLoss
                dtype=labels.dtype,
                device=labels.device
            )
            labels = torch.cat([omics_labels, labels], dim=1)

        return None, position_ids, attention_mask, past_key_values, inputs_embeds, labels


class LlaoaMetaForCausalLM(ABC):
    """
    Base class for LLaOA causal language models with omics integration.
    """

    @abstractmethod
    def get_model(self):
        """Return the underlying model."""
        pass

    def get_omics_tower(self):
        """Get the omics tower from the model."""
        return self.get_model().get_omics_tower()

    def encode_omics(self, omics_data):
        """Encode omics data using the model's omics tower."""
        return self.get_model().encode_omics(omics_data)

    def prepare_inputs_labels_for_multimodal(
        self, input_ids, position_ids, attention_mask, past_key_values, labels, omics_data
    ):
        """Prepare inputs for multimodal (omics + text) processing."""
        return self.get_model().prepare_inputs_labels_for_multimodal(
            input_ids, position_ids, attention_mask, past_key_values, labels, omics_data
        )