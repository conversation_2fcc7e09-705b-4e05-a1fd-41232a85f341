import torch.nn as nn
import re

def build_omics_projector(config, **kwargs):
    projector_type = getattr(config, 'omics_projector_type', 'linear')
    omics_hidden_size = getattr(config, 'omics_hidden_size', 128)
    lm_hidden_size = getattr(config, 'hidden_size', 4096)

    if projector_type == 'linear':
        return nn.Linear(omics_hidden_size, lm_hidden_size)
    elif projector_type.startswith('mlp'):
        # Handle different MLP formats
        if '_gelu' in projector_type:
            # Format: mlp2x_gelu
            mlp_match = re.match(r'^mlp(\d+)x_gelu$', projector_type)
            if mlp_match:
                depth = int(mlp_match.group(1))
                if depth == 2:
                    # Special case for mlp2x_gelu
                    return nn.Sequential(
                        nn.Linear(omics_hidden_size, omics_hidden_size * 2),
                        nn.GELU(),
                        nn.Linear(omics_hidden_size * 2, lm_hidden_size)
                    )
                elif depth == 3:
                    # Special case for mlp3x_gelu
                    return nn.Sequential(
                        nn.Linear(omics_hidden_size, omics_hidden_size * 2),
                        nn.GELU(),
                        nn.Linear(omics_hidden_size * 2, omics_hidden_size * 2),
                        nn.GELU(),
                        nn.Linear(omics_hidden_size * 2, lm_hidden_size)
                    )
                else:
                    # Generic case
                    layers = [nn.Linear(omics_hidden_size, lm_hidden_size)]
                    for _ in range(1, depth):
                        layers.append(nn.GELU())
                        layers.append(nn.Linear(lm_hidden_size, lm_hidden_size))
                    return nn.Sequential(*layers)
        else:
            # Format: mlp2x
            depth = int(projector_type.replace('mlp', '').replace('x', ''))
            layers = [nn.Linear(omics_hidden_size, lm_hidden_size), nn.ReLU()]
            for _ in range(1, depth):
                layers.append(nn.Linear(lm_hidden_size, lm_hidden_size))
                layers.append(nn.ReLU())
            return nn.Sequential(*layers)
    else:
        raise ValueError(f'Unknown omics projector type: {projector_type}')