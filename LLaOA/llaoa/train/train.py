import os
import torch
import argparse
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from torch.utils.data import DataLoader, DistributedSampler
from torch.nn.parallel import DistributedDataParallel as DDP
from transformers import (
    AutoTokenizer,
    get_linear_schedule_with_warmup,
    get_cosine_schedule_with_warmup,
    Trainer,
    TrainingArguments,
    HfArgumentParser
)
from accelerate import Accelerator
from accelerate.utils import set_seed
from accelerate.logging import get_logger

from llaoa.model.builder import load_pretrained_model
from llaoa.model.config import LlaoaConfig, OmicsTowerConfig
from llaoa.data.omics_qa_dataset import OmicsQADataset

# Set up logging
logger = get_logger(__name__)

@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune.
    """
    model_path: Optional[str] = None
    model_base: Optional[str] = None
    compass_model_path: str = None  # Will be resolved at runtime
    feature_type: str = "gene_level"
    projector_type: str = "mlp2x_gelu"
    lora_rank: Optional[int] = None
    lora_alpha: Optional[float] = None
    lora_dropout: Optional[float] = None
    lora_target_modules: Optional[List[str]] = None

    def __post_init__(self):
        # Resolve COMPASS model path if not explicitly provided
        if self.compass_model_path is None:
            from pathlib import Path
            # Try multiple possible locations for COMPASS model
            possible_paths = [
                "./COMPASS/example/model/pretrainer.pt",
                "../COMPASS/example/model/pretrainer.pt",
                str(Path(__file__).parents[2] / "COMPASS/example/model/pretrainer.pt"),
                str(Path(__file__).parents[3] / "COMPASS/example/model/pretrainer.pt")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.compass_model_path = path
                    break
            if self.compass_model_path is None:
                self.compass_model_path = "./COMPASS/example/model/pretrainer.pt"  # Default fallback

@dataclass
class DataArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    """
    rna_seq_path: str = None  # Will be resolved at runtime
    qa_json_path: str = None  # Will be resolved at runtime
    sample_id_col: Optional[str] = None
    max_length: int = 512
    validation_split: float = 0.1
    seed: int = 42

    def __post_init__(self):
        # Resolve RNA-seq path if not explicitly provided
        if self.rna_seq_path is None:
            from pathlib import Path
            # Try multiple possible locations
            possible_paths = [
                "COMPASS/example/data/compass_gide_tpm.tsv",
                "../COMPASS/example/data/compass_gide_tpm.tsv",
                str(Path(__file__).parents[2] / "COMPASS/example/data/compass_gide_tpm.tsv"),
                str(Path(__file__).parents[3] / "COMPASS/example/data/compass_gide_tpm.tsv")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.rna_seq_path = path
                    break
            if self.rna_seq_path is None:
                self.rna_seq_path = "COMPASS/example/data/compass_gide_tpm.tsv"  # Default fallback

        # Resolve QA JSON path if not explicitly provided
        if self.qa_json_path is None:
            from pathlib import Path
            # Try multiple possible locations
            possible_paths = [
                "data/omics_qa.json",
                "../data/omics_qa.json",
                str(Path(__file__).parents[2] / "data/omics_qa.json"),
                str(Path(__file__).parents[3] / "data/omics_qa.json")
            ]
            for path in possible_paths:
                if Path(path).exists():
                    self.qa_json_path = path
                    break
            if self.qa_json_path is None:
                self.qa_json_path = "data/omics_qa.json"  # Default fallback

def omics_collate_fn(batch):
    """
    Collate function for batching omics data and QA pairs.

    Args:
        batch: List of dictionaries with 'omics', 'question_ids', and 'answer_ids'

    Returns:
        Dictionary with batched data
    """
    # Collate omics DataFrame rows into a single DataFrame
    omics_list = [item['omics'] for item in batch]
    omics_df = pd.concat(omics_list, axis=0)

    # Pad sequences for question and answer IDs
    question_ids = torch.nn.utils.rnn.pad_sequence(
        [item['question_ids'] for item in batch],
        batch_first=True,
        padding_value=0
    )
    answer_ids = torch.nn.utils.rnn.pad_sequence(
        [item['answer_ids'] for item in batch],
        batch_first=True,
        padding_value=0
    )

    return {
        'omics': omics_df,
        'question_ids': question_ids,
        'answer_ids': answer_ids
    }

def create_model_and_tokenizer(model_args):
    """
    Create or load a model and tokenizer based on the provided arguments.

    Args:
        model_args: Model arguments

    Returns:
        Tuple of (tokenizer, model)
    """
    # Load model and tokenizer
    if model_args.model_path is not None:
        # Load pretrained model
        logger.info(f"Loading pretrained model from {model_args.model_path}")
        tokenizer, model, _, _ = load_pretrained_model(
            model_path=model_args.model_path,
            model_base=model_args.model_base
        )
    else:
        # Create new model with base LLM
        if model_args.model_base is None:
            raise ValueError("Either model_path or model_base must be provided")

        logger.info(f"Creating new model with base {model_args.model_base}")

        # Load tokenizer from base model
        tokenizer = AutoTokenizer.from_pretrained(model_args.model_base, use_fast=False)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # Create config with COMPASS encoder
        config = LlaoaConfig(
            omics_tower=OmicsTowerConfig(
                model_path=model_args.compass_model_path,
                feature_type=model_args.feature_type
            ),
            omics_projector_type=model_args.projector_type,
            omics_hidden_size=None  # Will be set automatically based on feature_type
        )

        # Load model
        _, model, _, _ = load_pretrained_model(
            model_path=model_args.model_base,
            config=config
        )

    # Apply LoRA if specified
    if model_args.lora_rank is not None:
        try:
            from peft import LoraConfig, get_peft_model

            logger.info(f"Applying LoRA with rank {model_args.lora_rank}")

            # Default target modules if not specified
            if model_args.lora_target_modules is None:
                model_args.lora_target_modules = ["q_proj", "v_proj"]

            # Create LoRA config
            lora_config = LoraConfig(
                r=model_args.lora_rank,
                lora_alpha=model_args.lora_alpha or model_args.lora_rank * 2,
                lora_dropout=model_args.lora_dropout or 0.05,
                target_modules=model_args.lora_target_modules,
                bias="none",
                task_type="CAUSAL_LM"
            )

            # Apply LoRA
            model = get_peft_model(model, lora_config)
            model.print_trainable_parameters()
        except ImportError:
            logger.warning("PEFT not installed, skipping LoRA application")

    return tokenizer, model

def create_datasets(tokenizer, data_args):
    """
    Create training and validation datasets.

    Args:
        tokenizer: Tokenizer to use for tokenization
        data_args: Data arguments

    Returns:
        Tuple of (train_dataset, val_dataset)
    """
    # Load full dataset
    full_dataset = OmicsQADataset(
        rna_seq_path=data_args.rna_seq_path,
        qa_json_path=data_args.qa_json_path,
        tokenizer=tokenizer,
        max_length=data_args.max_length,
        sample_id_col=data_args.sample_id_col
    )

    # Split into train and validation
    if data_args.validation_split > 0:
        # Set seed for reproducibility
        np.random.seed(data_args.seed)

        # Get dataset size
        dataset_size = len(full_dataset)

        # Create indices
        indices = list(range(dataset_size))
        np.random.shuffle(indices)

        # Calculate split point
        split = int(np.floor(data_args.validation_split * dataset_size))

        # Create train and validation indices
        train_indices, val_indices = indices[split:], indices[:split]

        # Create train and validation datasets
        from torch.utils.data import Subset
        train_dataset = Subset(full_dataset, train_indices)
        val_dataset = Subset(full_dataset, val_indices)

        logger.info(f"Created train dataset with {len(train_dataset)} samples")
        logger.info(f"Created validation dataset with {len(val_dataset)} samples")

        return train_dataset, val_dataset
    else:
        logger.info(f"Using full dataset with {len(full_dataset)} samples for training")
        return full_dataset, None

def train():
    """Main training function."""
    # Parse arguments
    parser = HfArgumentParser((ModelArguments, DataArguments, TrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    # Set up accelerator
    accelerator = Accelerator(
        gradient_accumulation_steps=training_args.gradient_accumulation_steps,
        mixed_precision=training_args.fp16 and "fp16" or training_args.bf16 and "bf16" or "no"
    )

    # Set seed for reproducibility
    set_seed(training_args.seed)

    # Create output directory if it doesn't exist
    if accelerator.is_main_process:
        os.makedirs(training_args.output_dir, exist_ok=True)

    # Create model and tokenizer
    tokenizer, model = create_model_and_tokenizer(model_args)

    # Create datasets
    train_dataset, val_dataset = create_datasets(tokenizer, data_args)

    # Create dataloaders
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=training_args.per_device_train_batch_size,
        shuffle=not isinstance(train_dataset, DistributedSampler),
        collate_fn=omics_collate_fn,
        num_workers=training_args.dataloader_num_workers
    )

    if val_dataset is not None:
        eval_dataloader = DataLoader(
            val_dataset,
            batch_size=training_args.per_device_eval_batch_size,
            shuffle=False,
            collate_fn=omics_collate_fn,
            num_workers=training_args.dataloader_num_workers
        )
    else:
        eval_dataloader = None

    # Set up optimizer
    no_decay = ["bias", "LayerNorm.weight"]
    optimizer_grouped_parameters = [
        {
            "params": [p for n, p in model.named_parameters() if not any(nd in n for nd in no_decay)],
            "weight_decay": training_args.weight_decay,
        },
        {
            "params": [p for n, p in model.named_parameters() if any(nd in n for nd in no_decay)],
            "weight_decay": 0.0,
        },
    ]
    optimizer = torch.optim.AdamW(
        optimizer_grouped_parameters,
        lr=training_args.learning_rate,
        betas=(training_args.adam_beta1, training_args.adam_beta2),
        eps=training_args.adam_epsilon
    )

    # Set up learning rate scheduler
    num_update_steps_per_epoch = len(train_dataloader) // training_args.gradient_accumulation_steps
    max_train_steps = training_args.num_train_epochs * num_update_steps_per_epoch

    if training_args.lr_scheduler_type == "cosine":
        lr_scheduler = get_cosine_schedule_with_warmup(
            optimizer=optimizer,
            num_warmup_steps=training_args.warmup_steps,
            num_training_steps=max_train_steps
        )
    else:
        lr_scheduler = get_linear_schedule_with_warmup(
            optimizer=optimizer,
            num_warmup_steps=training_args.warmup_steps,
            num_training_steps=max_train_steps
        )

    # Prepare everything with accelerator
    model, optimizer, train_dataloader, eval_dataloader, lr_scheduler = accelerator.prepare(
        model, optimizer, train_dataloader, eval_dataloader, lr_scheduler
    )

    # Get total batch size for logging
    total_batch_size = (
        training_args.per_device_train_batch_size
        * accelerator.num_processes
        * training_args.gradient_accumulation_steps
    )

    # Log info
    logger.info("***** Running training *****")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num Epochs = {training_args.num_train_epochs}")
    logger.info(f"  Instantaneous batch size per device = {training_args.per_device_train_batch_size}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {training_args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {max_train_steps}")

    # Training loop
    completed_steps = 0
    progress_bar = accelerator.get_progress_bar(max_train_steps)

    for epoch in range(int(training_args.num_train_epochs)):
        model.train()
        total_loss = 0

        for step, batch in enumerate(train_dataloader):
            # Forward pass
            with accelerator.accumulate(model):
                outputs = model(
                    input_ids=batch['question_ids'],
                    omics_data=batch['omics'],
                    labels=batch['answer_ids']
                )

                loss = outputs.loss
                total_loss += loss.detach().float()

                # Backward pass
                accelerator.backward(loss)

                # Update weights
                if accelerator.sync_gradients:
                    accelerator.clip_grad_norm_(model.parameters(), training_args.max_grad_norm)

                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()

            # Update progress bar
            if accelerator.sync_gradients:
                progress_bar.update(1)
                completed_steps += 1

                # Log loss
                if completed_steps % training_args.logging_steps == 0:
                    avg_loss = total_loss.item() / training_args.logging_steps
                    logger.info(f"Epoch: {epoch}, Step: {completed_steps}, Loss: {avg_loss:.4f}")
                    total_loss = 0

            # Save checkpoint
            if (completed_steps % training_args.save_steps == 0
                and completed_steps > 0
                and accelerator.is_main_process):
                output_dir = os.path.join(training_args.output_dir, f"checkpoint-{completed_steps}")
                accelerator.save_state(output_dir)
                logger.info(f"Saved checkpoint to {output_dir}")

            # Evaluate
            if (eval_dataloader is not None
                and completed_steps % training_args.eval_steps == 0
                and completed_steps > 0):
                model.eval()
                eval_loss = 0
                eval_steps = 0

                for eval_step, eval_batch in enumerate(eval_dataloader):
                    with torch.no_grad():
                        outputs = model(
                            input_ids=eval_batch['question_ids'],
                            omics_data=eval_batch['omics'],
                            labels=eval_batch['answer_ids']
                        )

                        eval_loss += outputs.loss.detach().float()
                        eval_steps += 1

                eval_loss = eval_loss / eval_steps
                logger.info(f"Epoch: {epoch}, Step: {completed_steps}, Eval Loss: {eval_loss:.4f}")

                model.train()

            # Break if max steps reached
            if completed_steps >= max_train_steps:
                break

        # Save checkpoint after each epoch
        if accelerator.is_main_process:
            output_dir = os.path.join(training_args.output_dir, f"checkpoint-epoch-{epoch+1}")
            accelerator.save_state(output_dir)
            logger.info(f"Saved checkpoint to {output_dir}")

    # Save final model
    if accelerator.is_main_process:
        final_dir = os.path.join(training_args.output_dir, "final")
        accelerator.save_state(final_dir)
        logger.info(f"Saved final model to {final_dir}")

        # Unwrap model
        unwrapped_model = accelerator.unwrap_model(model)

        # Save model and tokenizer
        unwrapped_model.save_pretrained(
            final_dir,
            is_main_process=accelerator.is_main_process,
            save_function=accelerator.save
        )
        tokenizer.save_pretrained(final_dir)
        logger.info(f"Saved model and tokenizer to {final_dir}")

if __name__ == '__main__':
    train()