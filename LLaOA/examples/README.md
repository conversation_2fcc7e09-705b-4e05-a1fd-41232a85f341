# LLaOA Example Notebooks

This directory contains example notebooks demonstrating how to use the LLaOA framework for various tasks.

## Available Examples

1. [Data Preparation](01_data_preparation.ipynb): Demonstrates how to prepare omics data and QA pairs for LLaOA
2. [Model Training](02_model_training.ipynb): Shows how to train a LLaOA model
3. [Model Evaluation](03_model_evaluation.ipynb): Explains how to evaluate a trained LLaOA model
4. [Inference and Visualization](04_inference_visualization.ipynb): Demonstrates inference with a trained model and visualization of results
5. [Custom Encoder Integration](05_custom_encoder.ipynb): Shows how to integrate a custom omics encoder

## Prerequisites

To run these notebooks, you need:

1. LLaOA installed (see [Installation Guide](../docs/installation.md))
2. Jupyter Notebook or JupyterLab
3. Additional dependencies:
   ```bash
   pip install matplotlib seaborn pandas scikit-learn
   ```

## Getting Started

1. Start Jupyter:
   ```bash
   jupyter notebook
   ```
   or
   ```bash
   jupyter lab
   ```

2. Navigate to this directory and open the desired notebook

## Additional Resources

For more information, refer to the [LLaOA Documentation](../docs/).
