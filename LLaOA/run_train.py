#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run training script for LLaOA with distributed training and mixed precision.
"""

import os
import sys
import argparse
from typing import Optional
from pathlib import Path

# Ensure the package is in the Python path
current_dir = Path(__file__).parent.absolute()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

def parse_args():
    parser = argparse.ArgumentParser(description="Run LLaOA training")

    # Model arguments
    parser.add_argument("--model-path", type=str, default=None,
                        help="Path to pretrained model")
    parser.add_argument("--model-base", type=str, default=None,
                        help="Base model for LoRA or new model")
    parser.add_argument("--compass-model-path", type=str,
                        default="./COMPASS/example/model/pretrainer.pt",
                        help="Path to COMPASS model")
    parser.add_argument("--feature-type", type=str, default="gene_level",
                        choices=["gene_level", "geneset_level", "concept_level", "vector"],
                        help="Type of features to extract from COMPASS")
    parser.add_argument("--projector-type", type=str, default="mlp2x_gelu",
                        choices=["linear", "mlp2x_gelu", "mlp3x_gelu"],
                        help="Type of projector to use")

    # LoRA arguments
    parser.add_argument("--lora-rank", type=int, default=None,
                        help="Rank of LoRA adaptation")
    parser.add_argument("--lora-alpha", type=float, default=None,
                        help="Alpha parameter for LoRA")
    parser.add_argument("--lora-dropout", type=float, default=None,
                        help="Dropout probability for LoRA")
    parser.add_argument("--lora-target-modules", type=str, nargs="+", default=None,
                        help="Target modules for LoRA")

    # Data arguments
    parser.add_argument("--rna-seq-path", type=str,
                        default="COMPASS/example/data/compass_gide_tpm.tsv",
                        help="Path to RNAseq data")
    parser.add_argument("--qa-json-path", type=str, default="data/omics_qa.json",
                        help="Path to QA pairs JSON")
    parser.add_argument("--sample-id-col", type=str, default=None,
                        help="Column name for sample ID in RNAseq data")
    parser.add_argument("--max-length", type=int, default=512,
                        help="Maximum sequence length")
    parser.add_argument("--validation-split", type=float, default=0.1,
                        help="Fraction of data to use for validation")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")

    # Training arguments
    parser.add_argument("--output-dir", type=str, default="./checkpoints",
                        help="Directory to save model checkpoints")
    parser.add_argument("--num-train-epochs", type=float, default=3.0,
                        help="Number of training epochs")
    parser.add_argument("--per-device-train-batch-size", type=int, default=4,
                        help="Batch size per device for training")
    parser.add_argument("--per-device-eval-batch-size", type=int, default=4,
                        help="Batch size per device for evaluation")
    parser.add_argument("--gradient-accumulation-steps", type=int, default=1,
                        help="Number of gradient accumulation steps")
    parser.add_argument("--learning-rate", type=float, default=1e-5,
                        help="Learning rate")
    parser.add_argument("--weight-decay", type=float, default=0.0,
                        help="Weight decay")
    parser.add_argument("--warmup-steps", type=int, default=100,
                        help="Number of warmup steps")
    parser.add_argument("--lr-scheduler-type", type=str, default="linear",
                        choices=["linear", "cosine"],
                        help="Type of learning rate scheduler")
    parser.add_argument("--logging-steps", type=int, default=10,
                        help="Number of steps between logging")
    parser.add_argument("--eval-steps", type=int, default=100,
                        help="Number of steps between evaluations")
    parser.add_argument("--save-steps", type=int, default=500,
                        help="Number of steps between saving checkpoints")
    parser.add_argument("--save-total-limit", type=int, default=3,
                        help="Maximum number of checkpoints to keep")
    parser.add_argument("--fp16", action="store_true",
                        help="Use mixed precision training (fp16)")
    parser.add_argument("--bf16", action="store_true",
                        help="Use mixed precision training (bf16)")
    parser.add_argument("--dataloader-num-workers", type=int, default=4,
                        help="Number of dataloader workers")
    parser.add_argument("--local_rank", type=int, default=-1,
                        help="Local rank for distributed training")

    return parser.parse_args()

def main():
    args = parse_args()

    # Convert arguments to format expected by HfArgumentParser
    model_args = [
        f"--model_path={args.model_path}" if args.model_path else "",
        f"--model_base={args.model_base}" if args.model_base else "",
        f"--compass_model_path={args.compass_model_path}",
        f"--feature_type={args.feature_type}",
        f"--projector_type={args.projector_type}",
        f"--lora_rank={args.lora_rank}" if args.lora_rank else "",
        f"--lora_alpha={args.lora_alpha}" if args.lora_alpha else "",
        f"--lora_dropout={args.lora_dropout}" if args.lora_dropout else "",
    ]

    if args.lora_target_modules:
        for module in args.lora_target_modules:
            model_args.append(f"--lora_target_modules={module}")

    data_args = [
        f"--rna_seq_path={args.rna_seq_path}",
        f"--qa_json_path={args.qa_json_path}",
        f"--sample_id_col={args.sample_id_col}" if args.sample_id_col else "",
        f"--max_length={args.max_length}",
        f"--validation_split={args.validation_split}",
        f"--seed={args.seed}",
    ]

    training_args = [
        f"--output_dir={args.output_dir}",
        f"--num_train_epochs={args.num_train_epochs}",
        f"--per_device_train_batch_size={args.per_device_train_batch_size}",
        f"--per_device_eval_batch_size={args.per_device_eval_batch_size}",
        f"--gradient_accumulation_steps={args.gradient_accumulation_steps}",
        f"--learning_rate={args.learning_rate}",
        f"--weight_decay={args.weight_decay}",
        f"--warmup_steps={args.warmup_steps}",
        f"--lr_scheduler_type={args.lr_scheduler_type}",
        f"--logging_steps={args.logging_steps}",
        f"--eval_steps={args.eval_steps}",
        f"--save_steps={args.save_steps}",
        f"--save_total_limit={args.save_total_limit}",
        "--fp16" if args.fp16 else "",
        "--bf16" if args.bf16 else "",
        f"--dataloader_num_workers={args.dataloader_num_workers}",
        f"--local_rank={args.local_rank}",
        "--report_to=none",  # Disable wandb, etc.
    ]

    # Filter out empty arguments
    model_args = [arg for arg in model_args if arg]
    data_args = [arg for arg in data_args if arg]
    training_args = [arg for arg in training_args if arg]

    # Combine all arguments
    all_args = model_args + data_args + training_args

    # Update sys.argv
    sys.argv = [sys.argv[0]] + all_args

    # Import and run training
    from llaoa.train.train import train
    train()

if __name__ == "__main__":
    main()
